<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" class="iosSafeArea">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="viewport-fit=cover,width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta content="telephone=no" name="format-detection" />
    <meta http-equiv="Expires" content="-1" />
    <meta http-equiv="Cache-Control" content="no-cache" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta name="browsermode" content="application" />
    <meta name="x5-page-mode" content="app" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title></title>
    <script
      src="/bc-h5-view/views/TKFlow/TKFlowEngine2.0.0.min.js"
      type="text/javascript"
    ></script>
    <script src="/bc-h5-view/views/bridge.js" type="text/javascript" ></script>
    <script>
			if (location.host.indexOf('yongjinbao.com.cn') > -1) {
				document.write('<script id="BonreeAgent" src="https://apmh5.yongjinbao.com.cn/BonreeSDK_JS.min.js" data=' + '\'{ "appId": "047dfa8fbb8a451caf21132f0422bc1a","uploadAddrHttps": "https://apm.yongjinbao.com.cn:10443/RUM/upload","uploadAddrHttp": "https://apm.yongjinbao.com.cn:10443/RUM/upload"}\'' + '>' + '<' + '/' + 'script>');
			} else {
				document.write('<script id="BonreeAgent" src="https://apmh5.yongjinbao.com.cn/BonreeSDK_JS.min.js" data=' + '\'{ "appId": "a7c13386245d48d5b0e69766707ef41e","uploadAddrHttps": "https://bupload.yjbtest.com:58897/RUM/upload","uploadAddrHttp": "https://bupload.yjbtest.com:58897/RUM/upload"}\'' + '>' + '<' + '/' + 'script>');
			}
		</script>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but vue-cli3 doesn't work properly without JavaScript
        enabled. Please enable it to continue.</strong
      >
    </noscript>
    <div id="app" class="iosSafeArea"></div>
    <!-- built files will be auto injected -->
    <script>
      (function (win, export_obj) {
        win['LogAnalyticsObject'] = export_obj;
        if (!win[export_obj]) {
          function _collect() {
            _collect.q.push(arguments);
          }
          _collect.q = _collect.q || [];
          win[export_obj] = _collect;
        }
        win[export_obj].l = +new Date();
      })(window, 'collectEvent');
    </script>
    <!-- <script
      async
      src="https://fzwww.yjbtest.com:444/cdn/track/collect.js"
    ></script> -->
    <script async src="https://www.yongjinbao.com.cn/cdn/track/collect.js"></script>
    <script>
      function callNativeHandler(a, b, c) {
        window.WebViewJavascriptBridge
          ? 'undefined' != typeof c
            ? window.WebViewJavascriptBridge.callHandler(a, b, c)
            : window.WebViewJavascriptBridge.callHandler(a, b)
          : document.addEventListener(
              'WebViewJavascriptBridgeReady',
              function () {
                'undefined' != typeof c
                  ? window.WebViewJavascriptBridge.callHandler(a, b, c)
                  : window.WebViewJavascriptBridge.callHandler(a, b);
              },
              !1
            );
      }

      function registerWebHandler(a, b) {
        window.WebViewJavascriptBridge
          ? window.WebViewJavascriptBridge.registerHandler(a, b)
          : document.addEventListener(
              'WebViewJavascriptBridgeReady',
              function () {
                window.WebViewJavascriptBridge.registerHandler(a, b);
              },
              !1
            );
      }

      function connectWebViewJavascriptBridge() {
        window.WebViewJavascriptBridge
          ? window.WebViewJavascriptBridge.init(initWebViewJavascriptBridge)
          : document.addEventListener(
              'WebViewJavascriptBridgeReady',
              function () {
                window.WebViewJavascriptBridge.init(initWebViewJavascriptBridge);
              },
              !1
            );
      }

      function initWebViewJavascriptBridge(a, b) {
        console.log('message:' + a), console.log('callback:' + b);
      }
      connectWebViewJavascriptBridge();

      window.callNativeHandler = callNativeHandler;

      (function (doc, win) {
        // 初始化或者转换横竖屏时设置跟元素字体大小
        var docEl = doc.documentElement,
          resizeEvt =
            'orientationchange' in window ? 'orientationchange' : 'resize',
          recalc = function () {
            var clientWidth = docEl.clientWidth;
            if (!clientWidth) return;
            if (clientWidth >= 375) {
              clientWidth = 375;
            }
            docEl.style.fontSize = 100 * (clientWidth / 375) + 'px';
          };
        // Abort if browser does not support addEventListener
        if (!doc.addEventListener) return;
        win.addEventListener(resizeEvt, recalc, false);
        doc.addEventListener('DOMContentLoaded', recalc, false);
      })(document, window);
    </script>
  </body>
</html>
