# 010087业务普通与信用联动需求实现说明

## 需求描述
针对010087这个业务，新增普通与信用联动需求：
勾选普通账户（包括沪A）取消权限，判断对应市场信用证券账户是否已取消或已勾选，若未取消或未勾选则弹窗提示客户"取消普通账户权限前需先取消对应市场信用账户权限，请同时勾选对应市场信用账户。"点击【我知道了】按钮后，回到选择账户列表页。取消信用账户权限不需要要判断普通已开通的账户权限情况。

## 实现方案

### 1. 修改selectAcc方法
在`src/components/register/holderAccountSelection/accountSelection.vue`文件的`selectAcc`方法中添加了新的检查逻辑：

```javascript
// 010087业务普通与信用联动需求：勾选普通账户（包括沪A）取消权限时的检查
if (
  this.bizType010087 &&
  this.$attrs.needCancel &&
  !isChecked &&
  assetProp !== ASSET_PROP.CREDIT_ACCOUNT
) {
  // 当前要勾选的是普通账户（包括沪A），需要检查对应市场的信用账户状态
  const hasCreditAccountInSameMarket =
    this.checkCreditAccountInSameMarket(exchangeType);
  if (hasCreditAccountInSameMarket) {
    this.$TAlert({
      title: '温馨提示',
      tips: '取消普通账户权限前需先取消对应市场信用账户权限，请同时勾选对应市场信用账户。',
      confirmBtn: '我知道了'
    });
    return;
  }
}
```

### 2. 新增检查方法
添加了`checkCreditAccountInSameMarket`方法来检查同一市场是否存在未取消的信用账户：

```javascript
/**
 * 检查同一市场是否存在信用账户且未取消或未勾选
 * @param {string} exchangeType - 交易市场类型
 * @returns {boolean} - 是否存在需要先取消的信用账户
 */
checkCreditAccountInSameMarket(exchangeType) {
  let accountListArr = [];
  this.accountInfoList.forEach(({ stockAccList }) => {
    stockAccList.forEach((item) => accountListArr.push(item));
  });

  // 查找同一市场的信用账户
  const creditAccountsInSameMarket = accountListArr.filter(
    (account) =>
      account.assetProp === ASSET_PROP.CREDIT_ACCOUNT &&
      account.exchangeType === exchangeType
  );

  if (creditAccountsInSameMarket.length === 0) {
    return false; // 没有信用账户，不需要检查
  }

  // 检查信用账户是否已取消或已勾选
  const hasUncancelledCreditAccount = creditAccountsInSameMarket.some(
    (account) => !account.isChecked && account.holderRightsDesc === '已开通'
  );

  return hasUncancelledCreditAccount;
}
```

## 实现逻辑说明

### 触发条件
1. `this.bizType010087` - 当前业务是010087（取消科创板权限）
2. `this.$attrs.needCancel` - 当前是取消权限操作
3. `!isChecked` - 当前账户未被勾选（即要勾选它）
4. `assetProp !== ASSET_PROP.CREDIT_ACCOUNT` - 当前选择的不是信用账户（是普通账户）

### 检查逻辑
1. 获取所有账户列表
2. 筛选出同一市场（exchangeType相同）的信用账户
3. 检查这些信用账户是否存在未勾选且状态为"已开通"的账户
4. 如果存在，则弹出提示阻止操作

### 关键数据结构
- `ASSET_PROP.CREDIT_ACCOUNT` = '7' - 信用账户标识
- `EXCHANGE_TYPE.SH` = '1' - 沪市
- `EXCHANGE_TYPE.SZ` = '2' - 深市
- `holderRightsDesc` = '已开通' - 账户权限已开通状态

## 测试场景

### 场景1：正常取消普通账户（无信用账户）
- 用户勾选普通账户取消权限
- 同一市场无信用账户
- 预期：正常勾选，无提示

### 场景2：取消普通账户但信用账户未勾选
- 用户勾选普通账户取消权限
- 同一市场存在信用账户且未勾选
- 预期：弹出提示，阻止操作

### 场景3：取消普通账户且信用账户已勾选
- 用户勾选普通账户取消权限
- 同一市场存在信用账户且已勾选
- 预期：正常勾选，无提示

### 场景4：取消信用账户
- 用户勾选信用账户取消权限
- 预期：正常勾选，无需检查普通账户状态
