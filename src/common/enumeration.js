// 数据字典key
export const DICT_TYPE = {
  DEGREE_CODE: 'bc.common.degreeCode', // 学历
  PROFESSION_CODE: 'bc.common.professionCode', // 职业
  POST_CODE: 'bc.common.postCode', // 职务
  INCOME: 'bc.common.income', // 年收入
  SOCIALREL: 'bc.common.socialRel', //社会关系
  INTEGRITYREC: 'bc.common.integrityRec', // 诚信记录
  PASSWORD_TYPE: 'bc.common.password_type', // 密码类别
  DIC_EXCHANGE_TYPE: 'bc.common.exchangeType', // 交易类别
  TAX_RESIDENT_PERSON: 'bc.common.taxResidentPerson', //个人税收居民身份
  NO_IDENTITYNO_REASON: 'bc.common.noIdentitynoReason', // 无纳税人识别号原因
  NATIONALITY: 'bc.common.nationality', // 国家

  PTRADEQMT_SYS_CONFIG: 'bc.business.010734.openSysInfoConfig' //PtradetQMT权限开通业务配置-系统编号
};

// 试题类型 //0单选 1多选
export const QUESTION_KIND = {
  SINGLE: '0',
  MULTI: '1'
};

// 准入条件质检结果 1通过 0不通过
export const RULE_RESULT = {
  pass: '1',
  fail: '0'
};

//股东账户状态 0正常 1冻结 2挂失 3销户 6小额休眠 7不合格 9公司不合格
export const HOLDER_STATUS = {
  NORMAL: '0'
};

// 业务开通权限标识 1有权限 0没有
export const IS_OPEN_RIGHTS = {
  possess: '1',
  none: '0'
};

// 适当性匹配标志
export const ELIG_RISK_FLAG = {
  SUCCESS: '1',
  FAIL: '0'
};

// 事件类型 //0 短信验证码;1 审核驳回; 2 办理成功; 3 办理失败
export const SMS_SEND_TYPE = {
  SMS: '0'
};

// 办理结果
export const BANDLE_STATE = {
  SUCCESS: '0',
  FAIL: '1000'
};

//流程状态 0：受理中，1：受理完成，2：受理作废，3：办理完成
export const PROCESS_STATUS = {
  ACCEPTING: '0',
  ACCEPT_COMPLETED: '1',
  ACCEPT_VOID: '2',
  COMPLETED: '3'
};

//任务类型 1：预审任务，2：终审任务，3：办理任务
export const TASK_TYPE = {
  READY_AUDIT_TASK: '1',
  OVER_AUDIT_TASK: '2',
  DO_TASK: '3'
};

//任务状态 0：待办，1：办理中，2：通过，3：驳回，4：取消
export const TASK_STATUS = {
  TO_DO: '0',
  DOING: '1',
  PASS: '2',
  REJECT: '3',
  CANCEL: '4'
};

// 交易类别 2深市 1沪市 9特转A
export const EXCHANGE_TYPE = {
  SZ: '2',
  SH: '1',
  TZA: '9'
};

// 资产属性
export const ASSET_PROP = {
  DERIVATIVES_ACCOUNT: 'B', // 衍生品账户
  GOLD_EXCHANGE_ACCOUNT: 'D', //黄金交易所账户
  OPTIONS_ACCOUNT: 'E', //期权账户
  ORDINARY_ACCOUNT: '0', //普通客户
  FUND_ACCOUNT: '1', // 基金账户
  CREDIT_ACCOUNT: '7', // 信用账户
  FINANCIAL_ACCOUNT: '8', //理财账户
  FUTURES_ACCOUNT: '9' //期货账户
};

// 证件类别
export const ID_KIND = {
  PERSONAL_ID: '0', //个人身份证
  HK_ID: 'i', //香港地区居民身份证
  MACAU_ID: 'j', //澳门地区居民身份证
  HK_MACAU_PASS: 'G', //港澳居民来往内地通行证
  TAIWAN_PASS: 'H', //台湾居民来往大陆通行证
  HK_MACAU_TAIWAN_ID: 'l', //港澳台居民居住证
  HK_MACAU_TAIWAN_PERMANENT_ID: 'o' //港澳永久性居民身F份证
};

// 留痕类型
export const MARK_TYPE = {
  PROFESSION_CODE_CONFIRM: '1', //职业与年龄不符
  ID_CARD_CONFIRM: '5', //身份证正反同一张确认
  ID_CARD_REAL_NAME_CHECK: '6', //身份证件信息收集用于实名验证
  HK_MAC_TAIWAN_CARD_DATE_CHECK: '7', //港澳台身份证件有效期核实
  PDPA: '11', //身份证件信息个保法确认
  QUESTIONNAIRE: '21', //问卷调查
  FUND_ACCOUNT_RETRIEVE_CONFIRM: '22', //找回资金账号身份信息收集确认留痕
};

//系统类型
export const SYS_CONFIG = {
  PTRADE: 'Ptrade',
  QMT: 'Qmt',
  DCZQCL: 'DCZQCL'
};
