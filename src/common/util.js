import {
  ecbEncrypt,
  ecbDecrypt,
  cbcEncrypt,
  cbcDecrypt
} from 'thinkive-hvue/plugin/sm/sm4';
import { decrypt } from 'thinkive-hvue/plugin/sm/sm2';
import { getDictData } from '@/service/service';
import {
  doAgreementRecord,
  doAgreementRecordExt,
  instantTokenGen,
  ssoLogin,
  tokenCheck
} from '@/service/service';
import ChannelUtil from '@/common/ChannelUtil';
import dayjs from 'dayjs';

/**
 * 对密码进行加密处理
 */
export function getPwdEncryption(pwd) {
  const sm4Key = $hvue.config.sm4Key;
  return ecbEncrypt(sm4Key, pwd);
}

/**
 * 对密码进行解密处理
 */
export function getPwdDecryption(pwd) {
  const sm4Key = $hvue.config.decSm4Key;
  return ecbDecrypt(sm4Key, pwd);
}

/**
 * 对密码进行加密处理
 */
export function getPwdCbcEncryption(pwd) {
  const sm4Key = $hvue.config.sm4KeyCbc;
  return cbcEncrypt(sm4Key, sm4Key, pwd);
}

/**
 * 对密码进行解密处理
 */
export function getPwdCbcDecryption(pwd) {
  const sm4Key = $hvue.config.decSm4KeyCbc;
  return cbcDecrypt(sm4Key, pwd);
}

async function request({ url = '', data = {}, method = 'POST' } = {}) {
  method = method.toUpperCase();
  data = JSON.stringify(data);
  if (window.fetch) {
    let requestConfig = {
      credentials: 'include', //为了在当前域名内自动发送cookie,必须提供这个选项
      method: method,
      headers: {
        Accept: 'application/json',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json'
      },
      mode: 'cors', //请求的模式
      cache: 'force-cache'
    };

    if (method === 'POST') {
      requestConfig.body = data;
    }

    try {
      const response = await fetch(url, requestConfig);
      const responseJson = await response.json();
      return responseJson;
    } catch (error) {
      throw new Error(error);
    }
  } else {
    return new Promise((resolve, reject) => {
      let requestObj;
      if (window.XMLHttpRequest) {
        requestObj = new XMLHttpRequest();
      } else {
        requestObj = new ActiveXObject();
      }

      requestObj.open(method, url, true);
      requestObj.setRequestHeader(
        'Content-type',
        'application/x-www-form-urlencoded'
      );
      requestObj.setRequestHeader('Cache-Control', 'no-cache');
      requestObj.send(data);
      requestObj.onreadystatechange = () => {
        if (requestObj.readyState == 4) {
          if (requestObj.status == 200) {
            let obj = requestObj.response;
            if (typeof obj !== 'object') {
              obj = JSON.parse(obj);
            }
            resolve(obj);
          } else {
            reject(requestObj);
          }
        }
      };
    });
  }
}

export { request };

/**
 * 是否支持webrtc
 * isJump 不支持时是否跳转
 */
export function isSupportWebRTC(callback) {
  var result = {
    error_no: 0,
    error_info: '支持'
  };
  var ua = navigator.userAgent.toLowerCase();

  function isSafari() {
    if (
      ua.indexOf('applewebkit') > -1 &&
      ua.indexOf('mobile') > -1 &&
      ua.indexOf('safari') > -1 &&
      ua.indexOf('linux') === -1 &&
      ua.indexOf('android') === -1 &&
      ua.indexOf('chrome') === -1 &&
      ua.indexOf('ios') === -1 &&
      ua.indexOf('browser') === -1
    ) {
      return true;
    }
    return false;
  }

  function getSafariVersion() {
    var verinfo = ua.match(/os [\d._]*/gi);
    var version = (verinfo + '').replace(/[^0-9|_.]/gi, '').replace(/_/gi, '.');
    return version;
  }

  function isExistWebRTCObject() {
    var RTCPeerConnection =
      window.RTCPeerConnection ||
      window.mozRTCPeerConnection ||
      window.webkitRTCPeerConnection ||
      '';
    var isSupportWebRTC = true;
    try {
      new RTCPeerConnection(null);
      isSupportWebRTC = navigator.mediaDevices ? true : false;
    } catch (e) {
      isSupportWebRTC = false;
    }
    return isSupportWebRTC;
  }

  function compareVersion(v1, v2) {
    if (v1 == v2) {
      return 0;
    }
    var vs1 = v1.split('.');
    var vs2 = v2.split('.');
    var length = Math.min(vs1.length, vs2.length);
    for (var i = 0; i < length; i++) {
      if (parseInt(vs1[i]) > parseInt(vs2[i])) {
        return 1;
      } else if (parseInt(vs1[i]) < parseInt(vs2[i])) {
        return -1;
      }
    }
    if (length == vs1.length) {
      return -1;
    } else {
      return 1;
    }
  }

  if (ua.indexOf('iphone') > -1) {
    if (compareVersion(getSafariVersion(), '11.3.0') <= 0) {
      result = {
        error_no: 3,
        error_info: 'ios系统版本低于11.3'
      };
    } else {
      if (!isExistWebRTCObject()) {
        result = {
          error_no: 1,
          error_info: '不存在webrtc对象'
        };
      }
    }
    callback(result);
    return result.error_no == 0;
  } else {
    if (!isExistWebRTCObject()) {
      result = {
        error_no: 1,
        error_info: '不存在webrtc对象'
      };
      callback(result);
      return result.error_no == 0;
    } else {
      if (!localStorage.getItem('supportWebrtc')) {
        navigator.mediaDevices
          .getUserMedia({
            video: true,
            audio: true
          })
          .then((stream) => {
            stream.getTracks().forEach(function (track) {
              track.stop();
            });
            localStorage.setItem('supportWebrtc', true);
            callback(result);
          })
          .catch((e) => {
            console.log(e);
            callback({
              error_no: 4,
              error_info: '尝试打开摄像头失败'
            });
          });
      } else {
        callback(result);
        return result.error_no == 0;
      }
    }
  }
}

export function loadJS(urls) {
  if (!Array.isArray(urls)) {
    urls = [urls];
  }
  let success = 0;
  return new Promise(function (resolve, reject) {
    urls.every((url) => {
      createScriptElement(
        url,
        function () {
          ++success;
          console.log(url + ',' + success + ',' + urls.length);
          if (success === urls.length) {
            resolve();
          }
        },
        function (e) {
          reject(e);
        }
      );
      return true;
    });
  });
}

/**
 * @desc 创建用户信息实例
 */
export class ClientInstance {
  constructor(info) {
    Object.assign(this, info);
    this.source = info;
  }

  findDiff() {
    let diff = {};
    Object.keys(this.source).forEach((key) => {
      if (this.source[key] !== this[key]) {
        diff[key] = this[key];
      }
    });
    return diff;
  }
}

// /**
//  * @desc 中文转拼音
//  */
// export async function getPinyinUtil(str, options = {}) {
//   const getData = await getPinyin(
//     {
//       chinese: str
//     },
//     options
//   );
//   if (getData.code === 0) {
//     return getData.data;
//   } else {
//     return '';
//   }
// }

// 修改时间格式话方法,用于格式化指定参数
Date.prototype.format = function (fmt) {
  let o = {
    'M+': this.getMonth() + 1, //月份
    'd+': this.getDate(), //日
    'h+': this.getHours(), //小时
    'm+': this.getMinutes(), //分
    's+': this.getSeconds(), //秒
    'q+': Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds() //毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (this.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return fmt;
};

export function uploadFile(
  url,
  file,
  listner = {
    success: () => {},
    error: () => {},
    progress: () => {}
  },
  param = {}
) {
  var xhr = new XMLHttpRequest();

  xhr.onreadystatechange = function () {
    if (xhr.readyState === 4 && 200 === xhr.status) {
      try {
        listner.success(JSON.parse(xhr.responseText));
      } catch (e) {
        console.log(e);
        // _hvueToast({ mes: '上传失败' });
      }
    }
  };
  if (xhr.upload) {
    // 	Data transfer is going on
    xhr.upload.addEventListener('progress', function (e) {
      var p = (e.loaded / e.total) * 100;
      listner.progress(p.toFixed(0));
    });
    // The fetch starts
    xhr.upload.addEventListener('loadstart', function (e) {
      console.log(e);
    });
    // The fetch operation didn't complete by the timeout the author specified
    xhr.upload.addEventListener('timeout', function (e) {
      console.log(e);
      listner.error(e);
    });
    // The fetch failed
    xhr.upload.addEventListener('error', function (e) {
      console.log(e);
      listner.error(e);
    });
    // The fetch operation was aborted
    xhr.upload.addEventListener('abort', function (e) {
      console.log(e);
      listner.error(e);
    });
  } else {
    listner.error(new Error('不支持进度条....'));
  }
  xhr.open('post', url, true);
  // xhr.setRequestHeader('Content-Disposition', 'multipart/form-data');
  xhr.setRequestHeader('Accept', 'application/json');
  xhr.setRequestHeader('Content-Type', 'application/json');
  xhr.setRequestHeader('Authorization', $h.getSession('authorization'));
  xhr.setRequestHeader(
    'tk-token-authorization',
    $h.getSession('authorization')
  );
  /*    var formData = new FormData();
    Object.keys(param).forEach(a => {
      formData.append(a, param[a]);
    });
    formData.append('imgContent', file);
 */
  try {
    xhr.send(JSON.stringify({ imgContent: file, ...param }));
  } catch (e) {
    listner.error(e);
  }
  return xhr;
}

export function uploadFileFormData(
  url,
  param,
  listner = {
    success: () => {},
    error: () => {},
    progress: () => {}
  }
  // param = {}
) {
  var xhr = new XMLHttpRequest();

  xhr.onreadystatechange = function () {
    if (xhr.readyState === 4 && 200 === xhr.status) {
      try {
        listner.success(JSON.parse(xhr.responseText));
      } catch (e) {
        console.log(e);
        _hvueToast({ mes: '上传失败' });
      }
    }
  };
  if (xhr.upload) {
    // 	Data transfer is going on
    xhr.upload.addEventListener('progress', function (e) {
      var p = (e.loaded / e.total) * 100;
      listner.progress(p.toFixed(0));
    });
    // The fetch starts
    xhr.upload.addEventListener('loadstart', function (e) {
      console.log(e);
    });
    // The fetch operation didn't complete by the timeout the author specified
    xhr.upload.addEventListener('timeout', function (e) {
      console.log(e);
      listner.error(e);
    });
    // The fetch failed
    xhr.upload.addEventListener('error', function (e) {
      console.log(e);
      listner.error(e);
    });
    // The fetch operation was aborted
    xhr.upload.addEventListener('abort', function (e) {
      console.log(e);
      listner.error(e);
    });
  } else {
    listner.error(new Error('不支持进度条....'));
  }
  xhr.open('post', url, true);
  xhr.setRequestHeader('Content-Disposition', 'multipart/form-data');
  xhr.setRequestHeader('Accept', 'application/json');
  // xhr.setRequestHeader('Content-Type', 'application/json');
  xhr.setRequestHeader('Authorization', $h.getSession('authorization'));
  xhr.setRequestHeader(
    'tk-token-authorization',
    $h.getSession('authorization')
  );
  var formData = new FormData();
  Object.keys(param).forEach((a) => {
    formData.append(a, param[a]);
  });

  try {
    xhr.send(formData);
  } catch (e) {
    listner.error(e);
  }
  return xhr;
}

/**
 * 身份证号获取出生日期
 * @param c 身份证号码 15或18位
 * @returns 返回格式 1994-07-01
 */
export function idCardToBirthday(c) {
  if (c) {
    if (c.length === 18) {
      return c.replace(/\d{6}(\d{4})(\d{2})(\d{2})\d{3}[\dXx]/, '$1-$2-$3');
    } else if (c.length === 15) {
      return c.replace(/\d{6}(\d{2})(\d{2})(\d{2})\d{3}/, '19$1-$2-$3');
    }
  }
}

/**
 * 根据身份证号获取距离指定日期的年龄
 * @param {*} a 身份证号码
 * @param {*} curDate 指定日志 默认为当前日志
 */
export function getAge(a, curDate = '') {
  var birthDay = idCardToBirthday(a);
  var birthDate = new Date(birthDay);
  curDate = curDate.replace(
    /^(\d{4})[.\-/](\d{1,2})[.\-/](\d{1,2})$/,
    '$1-$2-$3'
  );
  var nowDateTime = curDate ? new Date(curDate) : new Date();
  var age = nowDateTime.getFullYear() - birthDate.getFullYear();
  // 再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
  if (
    nowDateTime.getMonth() < birthDate.getMonth() ||
    (nowDateTime.getMonth() === birthDate.getMonth() &&
      nowDateTime.getDate() < birthDate.getDate())
  ) {
    age--;
  }
  return age;
}

/**
 * 身份证号获取性别值
 * @param c 身份证号码 15或18位
 * @returns {number}  0男 1女
 */
export function idCardToSex(c) {
  var i = 1;
  if (c) {
    i = c.length === 15 ? +c.charAt(14) % 2 : +c.charAt(16) % 2;
  }
  return (i + 1) % 2 == 0 ? 0 : 1;
}

/**
 * 查询字典方法 参数为对象
 * 可以直接传入key或者value来返回对应的值 比如[{a:1,b:2}]  传入key=a返回1 传入value=1返回a
 * 也可以不传key和value 直接返回整个字典数组
 * @param d{type 数据字典类型 Y, key 查询键, value 查询值}
 * @param f 回调函数 参数返回数组或对象 [{key:1, value:2}]
 */
export async function queryDictionary(d) {
  var dictionaryCacheName = 'dictionary_' + d.type;
  var callback = function (data) {
    if (data && data.length > 0) {
      var arr = [];
      for (var s = 0; s < data.length; s++) {
        var t = {
          key: data[s].dictValue, // key是编号
          value: data[s].dictLabel, // value是值
          type: d.type,
          index: s
        };
        if ((d.key && d.key === t.key) || (d.value && d.value === t.value)) {
          return t;
        }
        arr.push(t);
      }
      return arr;
    }
    return {};
  };

  let dc = sessionStorage.getItem(dictionaryCacheName);
  if (dc) {
    return callback(JSON.parse(dc));
  } else {
    return await getDictData({ dictType: d.type }).then((data) => {
      if (data.code === 0 && data.data?.[d.type]) {
        sessionStorage.setItem(
          dictionaryCacheName,
          JSON.stringify(data.data[d.type])
        );
        return callback(data.data[d.type]);
      } else {
        return {};
      }
    });
  }
}

/**
 * 将base64转换为blob
 * @param {*} dataurl
 * @returns
 */
export function dataURLtoFile(dataurl) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  let blob = new Blob([u8arr], { type: mime });
  blob.name = new Date().getTime() + '.' + mime.split('/')[1];
  return blob;
}

export function toVideoFormatDate(str) {
  return (str || '').replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
}

/**
 * 去掉图片base64前缀
 * @param {*} ndata
 * @returns
 */
export function filterBase64Pre(ndata) {
  let arr = ndata.split('base64,');
  return arr[arr.length - 1];
}

/**
 * 驼峰转换下划线
 */
export function camelToUnderline(str) {
  return str && str.replace(/([A-Z])/g, '_$1').toLowerCase();
}

/**
 * 下划线转换驼峰
 */
export function underlineToCamel(str) {
  return str && str.replace(/_(\w)/g, (all, letter) => letter.toUpperCase());
}
/*
 * 驼峰/下划线命名规范相互转化
 */
export function formatData(data) {
  let obj = {};
  for (let key in data) {
    const isCamel = key.match(/[a-z]+[A-Z][a-zA-Z]*/g); // 是否驼峰
    const isUnderline = key.match(/_/g); // 是否下划线
    if (isCamel) {
      obj[camelToUnderline(key)] = data[key];
    } else if (isUnderline) {
      obj[underlineToCamel(key)] = data[key];
    } else {
      obj[key] = data[key];
    }
  }
  return obj;
}

/*
 * 协议签署公共方法
 */
export function signAgree({ flowNodeNo, inProperty, outProperty }, agreeList) {
  return new Promise(async (resolve, reject) => {
    let doList;
    let epaperSignJson;
    try {
      epaperSignJson = JSON.parse(
        outProperty.epaperSignJson || inProperty.epaperSignJson
      );
      epaperSignJson = epaperSignJson.filter((a) => a.nodeId !== flowNodeNo);
    } catch (e) {
      console.log(e);
      epaperSignJson = [];
    }

    try {
      doList = await _doAgreementRecord({
        agreeList,
        flowNodeNo,
        outProperty
      });
    } catch (e) {
      reject(e);
    }
    Promise.all(doList)
      .then((dataList = []) => {
        let nodeId = flowNodeNo;
        let batchNo;
        dataList.forEach(({ signBatchno }) => {
          batchNo = signBatchno;
        });
        epaperSignJson.push({
          nodeId,
          batchNo
        });
        resolve(JSON.stringify([...epaperSignJson]));
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/*
 * 协议签署公共方法V2
 */
export function signAgreeV2(
  { flowNodeNo, inProperty, outProperty },
  agreeList,
  $attrs
) {
  return new Promise(async (resolve, reject) => {
    let doList;
    let epaperSignJson;
    try {
      epaperSignJson = JSON.parse(
        outProperty.epaperSignJson || inProperty.epaperSignJson
      );
      epaperSignJson = epaperSignJson.filter((a) => a.nodeId !== flowNodeNo);
    } catch (e) {
      console.log(e);
      epaperSignJson = [];
    }
    try {
      doList = await _doAgreementRecordV2({
        agreeList,
        flowNodeNo,
        outProperty,
        $attrs
      });
    } catch (e) {
      reject(e);
    }
    Promise.all(doList)
      .then((dataList = []) => {
        let nodeId = flowNodeNo;
        let batchNo;
        dataList.forEach(({ signBatchno }) => {
          batchNo = signBatchno;
        });
        epaperSignJson.push({
          nodeId,
          batchNo
        });
        resolve(JSON.stringify([...epaperSignJson]));
      })
      .catch((err) => {
        reject(err);
      });
  });
}

async function _doAgreementRecordV2({
  agreeList,
  flowNodeNo,
  inProperty,
  $attrs
}) {
  return new Promise(async (resolve, reject) => {
    let dataList = [];
    let signBatchno = '';
    let readTimeSum = 0;
    for (let i = 0; i < agreeList.length; i++) {
      readTimeSum += parseInt(agreeList[i].readTime);
    }
    let { agreementId, agreementVersion, agreementNodeNo } = agreeList[0];
    try {
      let { data } = await doAgreementRecordExt({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        agreementExt: $attrs?.agreementExt,
        agreementId,
        agreementVersion,
        readTime: readTimeSum,
        // agreementNo,
        agreementNodeNo,
        agreementBizType: $attrs.agreementNodeNo.split(':')[0],
        signBatchno
      });
      signBatchno = data.signBatchno;
      dataList.push(data);
    } catch (e) {
      reject(e);
    }
    // for (let {
    //   agreementId,
    //   agreementVersion,
    //   agreementNodeNo,
    //   agreementNo
    // } of agreeList) {
    //   try {
    //     let { data } = await doAgreementRecordExt({
    //       flowToken: sessionStorage.getItem('TKFlowToken'),
    //       agreementExt: $attrs?.agreementExt,
    //       agreementId,
    //       agreementVersion,
    //       readTime: readTimeSum,
    //       // agreementNo,
    //       agreementNodeNo,
    //       agreementBizType: $attrs.agreementNodeNo.split(':')[0],
    //       signBatchno
    //     });
    //     signBatchno = data.signBatchno;
    //     dataList.push(data);
    //   } catch (e) {
    //     reject(e);
    //   }
    // }
    resolve(dataList);
  });
}

async function _doAgreementRecord({ agreeList, flowNodeNo, inProperty }) {
  return new Promise(async (resolve, reject) => {
    let dataList = [];
    let signBatchno = '';
    for (let {
      contractType,
      agreementId,
      agreementVersion,
      readTime,
      groupId
    } of agreeList) {
      // const tokenRes = await getJwtToken({
      //   flowNo: flowNodeNo,
      //   businessType: inProperty.bizType
      // });
      // $h.setSession('jwtToken', tokenRes.data);
      try {
        let { data } = await doAgreementRecord({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          contractType,
          agreementId,
          agreementVersion,
          readTime,
          groupId,
          bizType: inProperty.bizType,
          signBatchno
        });
        signBatchno = data.signBatchno;
        dataList.push(data);
      } catch (e) {
        reject(e);
      }
    }
    resolve(dataList);
  });
}

/*
 * 国金H5对接统一登录执行方法(包含开户SDK处理方式以及对接思迪交易APP处理方式)
 */
export function H5ssoLoginUtil(token, opStation) {
  console.log('H5ssoLoginUtil');
  return new Promise((resolve, reject) => {
    tokenCheck({ codeToken: token, opStation })
      .then((loginData) => {
        console.log(loginData);
        if (loginData.code === 0) {
          resolve(loginData);
        } else {
          reject(loginData.msg);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/*
 * 国金APP对接统一登录执行方法(包含开户SDK处理方式以及对接思迪交易APP处理方式)
 */
export function APPssoLoginUtil(token, opStation) {
  console.log('APPssoLoginUtil');
  return new Promise((resolve, reject) => {
    tokenCheck({ codeToken: token, opStation })
      .then((loginData) => {
        console.log(loginData);
        if (loginData.code === 0) {
          resolve(loginData);
        } else {
          // reject(loginData.msg);
          const reqParam60099 = {
            funcNo: '60099',
            moduleName: $hvue.customConfig.moduleName,
            targetModule: $hvue.customConfig.moduleName,
            actionType: '1'
          };
          let { results } = $h.callMessageNative(reqParam60099);
          console.log(`60099 callback ********`);
          console.log(results);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
}

/*
 * 对接统一登录执行方法(包含开户SDK处理方式以及对接思迪交易APP处理方式)
 */
export function ssoLoginUtil() {
  return new Promise((resolve, reject) => {
    const appCode = $h.getSession('appCode');
    const channelUtil = new ChannelUtil({ appCode });
    if (channelUtil.isChannel) {
      const reqParam50041 = {
        funcNo: '50041',
        key: 'fxcAccountInfo'
      };
      let { error_no, error_info, results } =
        $h.callMessageNative(reqParam50041);
      console.log(`50041 callback ********`);
      console.log(results);
      if (![0, 1, '0', '1'].includes(error_no)) {
        _hvueToast({
          mes: error_info
        });
        return;
      }
      const loginInfo = results[0].value; // 判断内存中是否存在客户号
      if (loginInfo) {
        const { client_id } = loginInfo;
        toSsoLogin({ results: { fundAccount: client_id } });
      } else {
        const reqParam60099 = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          targetModule: $hvue.customConfig.moduleName,
          actionType: '1'
        };
        let { results } = $h.callMessageNative(reqParam60099);
        console.log(`60099 callback ********`);
        console.log(results);
      }
    } else {
      /*
       * 对接统一登录执行方法
       * 进入三方页面、点击需要登录的模块
       * 三方客户端通过调用原生60415插件判断是否登录
       * 未登录,通过60420插件拉起登录,登录成功返回三方页面.执行回调,通过回调获取账号与sm2加密的密码
       * 已登录,通过60419获取当前登录账户与加密的密码
       */
      const reqParam60415 = {
        funcNo: '60415',
        loginType: '1', // 登录类型(判断交易是否登录传1/判断手机号是否登录传2)
        accountType: 'A' //账户类型(判断资金账号传A/判断手机号传C)
      };
      let { results } = $h.callMessageNative(reqParam60415);
      console.log(`60415 callback ********`);
      console.log(results);
      if (results[0]?.isLogin === '1') {
        const reqParam60419 = {
          funcNo: '60419',
          loginType: '1', // 登录类型(判断交易是否登录传1/判断手机号是否登录传2)
          accountType: 'A' //账户类型(判断资金账号传A/判断手机号传C)
        };
        let res60419 = $h.callMessageNative(reqParam60419, toSsoLogin);
        console.log(`60419 callback ********`);
        console.log(res60419);
      } else {
        const reqParam60420 = {
          funcNo: '60420',
          loginType: '1', // 登录类型(判断交易是否登录传1/判断手机号是否登录传2)
          accountType: 'A' //账户类型(判断资金账号传A/判断手机号传C)
        };
        let res60420 = $h.callMessageNative(reqParam60420, toSsoLogin);
        console.log(`60420 callback ********`);
        console.log(res60420);
      }
    }
    function toSsoLogin({ results }) {
      // _hvueLoading.open();
      let reqParams;
      let { fundAccount, fundPwd } = results;
      if (channelUtil.isChannel) {
        reqParams = { clientId: 'encrypt:' + getPwdEncryption(fundAccount) };
      } else {
        fundPwd = decrypt($hvue.config.sm2Key, fundPwd);
        reqParams = {
          clientId: fundAccount,
          tradePassword: 'encrypt:' + getPwdEncryption(fundPwd)
        };
      }
      ssoLogin(reqParams)
        .then((loginData) => {
          if (loginData.code === 0) {
            resolve(loginData);
          } else {
            reject(loginData.msg);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }
  });
}

// 退出SDK
export function exitApp() {
  if (getUrlParam('routerBack') === 'true') {
    window.$router.back();
  } else {
    $h.callMessageNative({
      funcNo: '50114',
      moduleName: $hvue.customConfig.moduleName
    });
  }
}

export function getUrlParam(name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)'); //构造一个含有目标参数的正则表达式对象
  var r = window.location.search.substr(1).match(reg); //匹配目标参数
  if (r != null) return decodeURIComponent(r[2]);
  return null; //返回参数值
}

// 退出原生登录 -> 退出SDK
export function exitSsoLoginApp() {
  const reqParam60099 = {
    funcNo: '60099',
    moduleName: $hvue.customConfig.moduleName,
    targetModule: $hvue.customConfig.moduleName,
    actionType: '2'
  };
  let { results } = $h.callMessageNative(reqParam60099);
  console.log(`60099 callback ********`);
  console.log(results);
  exitApp();
}

/*
 * 唤起APP原生登录(国金证券对接佣金宝)
 * @type //1普通登录 2信用登录 3担保品划转登录（普通+信用） 16期权
 */
export function wakeLoginAppGJ(type = 1) {
  if (type?.constructor.name === 'Array') {
    type.forEach((t) => {
      const { result } = $h.callMessageNative({
        funcNo: '60099',
        actionType: '13',
        params: {
          yjbFuncId: '4001', //退出登录
          yjbFuncParams: { type: t }
        }
      });
      console.log(`60099 callback ******** type:${JSON.stringify(result)}`);
    });
  } else {
    const { result } = $h.callMessageNative({
      funcNo: '60099',
      actionType: '13',
      params: {
        yjbFuncId: '4001', //退出登录
        yjbFuncParams: { type }
      }
    });
    console.log(`60099 callback ******** type:${JSON.stringify(result)}`);
  }

  const { result2 } = $h.callMessageNative({
    funcNo: '60099',
    actionType: '13',
    params: {
      yjbFuncId: '2009', //跳转交易
      yjbFuncParams: {
        type: 2, //默认为0，返回客户端页面堆栈中的上一个页面。1，返回当前页面的前一个页面，例如A-消息-登录，则会直接返回到A页面。2，返回到首页。3，返回到指走url，前面只有首页。
        rootType: 3
      }
    }
  });
  console.log(`60099 callback ******** type:${JSON.stringify(result2)}`);

  exitApp();
}

/*
 * 唤起APP原生登录
 */
export function wakeLoginApp() {
  exitSsoLoginApp();
  const reqParam60420 = {
    funcNo: '60420',
    loginType: '1', // 登录类型(判断交易是否登录传1/判断手机号是否登录传2)
    accountType: 'A' //账户类型(判断资金账号传A/判断手机号传C)
  };
  let res60420 = $h.callMessageNative(reqParam60420);
  console.log(`60420 callback ********`);
  console.log(res60420);
}

// 获取终端信息
export function getOpStation() {
  let opStation = $h.getSession('opStation') || '';
  if (opStation !== '') return opStation;
  if ($hvue.platform !== '0') {
    const result = $h.callMessageNative({
      funcNo: '50001'
    });
    result.results = result.results || [];
    let data = result.results[0];
    console.log(`50001 callback ********`);
    console.log(data);
    if (result.error_no == '0' && data) {
      if (!$hvue.iBrowser.ios) {
        opStation +=
          'MA;IIP=' +
          (data.deviceIP || 'NA') +
          ';MAC=' +
          (data.deviceMAC || 'NA') +
          ';IMEI=' +
          (data.deviceMEID || 'NA') +
          ';RMPN=NA' +
          /*'${mobile_no}' +*/
          ';UMPN=NA' +
          /*'+86${mobile_no}' +*/
          ';ICCID=' +
          (data.deviceICCID || 'NA') +
          ';OSV=Android' +
          (data.deviceSysVersion || 'NA') +
          ';IMSI=' +
          (data.deviceIMSI || 'NA') +
          ';@KH-CBB' +
          ';@' +
          (data.softName || 'NA') +
          '+' +
          (data.softVersion || 'NA');
      } else {
        opStation +=
          'MI;IIP=' +
          (data.deviceIP || 'NA') +
          ';MAC=' +
          (data.deviceMAC || 'NA') +
          ';IDFV=' +
          (data.deviceMAC || 'NA') +
          ';RMPN=NA' +
          /* '${mobile_no}' +*/
          ';UMPN=NA' +
          /*'+86${mobile_no}' +*/
          ';ICCID=' +
          (data.deviceICCID || 'NA') +
          ';OSV=iOS' +
          (data.deviceSysVersion || 'NA') +
          ';IMSI=' +
          (data.deviceIMSI || 'NA') +
          ';@KH-CBB' +
          ';@' +
          (data.softName || 'NA') +
          '+' +
          (data.softVersion || 'NA');
      }
      opStation = opStation.replace(/null/g, 'NA');
      console.log('terminal_infos', opStation);
      if (opStation) $h.setSession('opStation', opStation);
      return opStation;
    }
  }
  if ($hvue.iBrowser.android) {
    opStation =
      'MA|IIP=NA|IPORT=NA|LIP=NA|MAC=NA|IMEI=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|mobile';
  } else if ($hvue.iBrowser.ios) {
    opStation =
      'MI|IIP=NA|IPORT=NA|LIP=NA|MAC=NA|IDFV=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|mobile';
  } else {
    opStation =
      'MI|IIP=NA|IPORT=NA|LIP=NA|MAC=NA|IDFV=NA|RMPN=NA|UMPN=NA|ICCID=NA|OSV=NA|IMSI=NA|NA|PC';
  }
  if (window.$router.currentRoute.query.opStation) {
    opStation = window.$router.currentRoute.query.opStation;
    if (opStation) $h.setSession('opStation', opStation);
  }
  if (window.$router.currentRoute.query.op_station) {
    opStation = window.$router.currentRoute.query.op_station;
    if (opStation) $h.setSession('opStation', opStation);
  }
  return opStation;
}

/*
 * 部件枚举类型根据数据字典key获取高级属性过滤后的数组对象
 */
export async function queryDictProps(dictKey, props) {
  var callback = function (data) {
    return data;
  };
  return await getDictData({ dictType: dictKey }).then((res) => {
    let objArr = res.data[dictKey];
    let arr = [];
    if (props) {
      arr = objArr.filter((item) => props.includes(item.dictValue));
    } else {
      arr = objArr;
    }
    return callback(arr);
  });
}

/*
 * 国金火山埋点方法
 */

/*
 * 自定义埋点采集事件
 */
export const trackEvent = (params) => {
  const { event_name, ...eventParams } = params;
  if (!event_name) {
    return 'event_name is empty!';
  }
  console.log(params);
  window.collectEvent(event_name, eventParams);
};

/*
 * 上报uuid信息
 */
export const trackUuid = (uuid) => {
  window.collectEvent('config', {
    user_unique_id: uuid,
    user_unique_id_type: 'client_id'
  });
};

/*
 * 上报公共属性信息
 */
export const trackPublicValue = (params) => {
  window.collectEvent('config', params);
};

/*
* 校验港澳台居民来往内地通行证
* 国家制定的编码规则：
    香港：H + 8位阿拉伯数字，总位数为9。
    澳门：M + 8位阿拉伯数字，总位数为9。
    台湾：8位阿拉伯数字
*/
export function checkHKPassCard(idNumber) {
  const regExp = [/[H]\d{8}/, /^[M]\d{8}$/, /^\d{8}$/];
  return regExp.some((a) => {
    return a.test(idNumber);
  });
}

/*
* 校验港澳台居民居住证
* 国家制定的编码规则：
    (1)总位数18位
    (2)香港以810000开头，澳门以820000开头，台湾以830000开头。
    (3)第7—14位数字表示：出生年、月、日；
    (4)第15、16位数字表示：所在地的派出所的代码；
    (5)第17位数字表示性别：奇数表示男性，偶数表示女性；
*/
export function checkHKLiveCard(idNumber) {
  const regExp = [
    /^8[123]0000(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dX]$/
  ];
  return regExp.some((a) => {
    return a.test(idNumber);
  });
}

/*
 * 金额单位换算
 */
export function moneyFormat(num = 0, mult, fixNum = false) {
  if (!num) return num.toFixed(fixNum);
  if (num.constructor.name === 'String') {
    num = parseFloat(num);
  }
  if (mult) {
    if (fixNum) {
      return (num * 10000).toFixed(fixNum);
    } else {
      return num * 10000;
    }
  } else {
    if (fixNum) {
      return (num / 10000).toFixed(fixNum);
    } else {
      return num / 10000;
    }
  }
}

/*
 * 数字向上取整
 */
export function mathCeil(numString) {
  if (numString !== '') {
    return Math.ceil(parseFloat(numString));
  } else {
    return '';
  }
}

/*
 * 数字向下取整
 */
export function mathFloor(numString) {
  if (numString !== '') {
    return Math.floor(parseFloat(numString));
  } else {
    return '';
  }
}

export function dateFormat(date) {
  if (!date) return date;
  return date.replace(/(\d{4})(\d{2})(\d{2})/, '$1.$2.$3');
}

/**
 * 计算时间之差(年份)
 * @param {String} d1 开始日期
 * @param {String} d2 结束日期
 * @param {String} y1 相差年份
 * @param {String} str 时间标识
 */
export function computeGetYears(d1, d2, y1, str = 'D') {
  d1 = dayjs(d1).add(y1, 'year').toDate();
  d2 = new Date(d2.replace(/\./g, '/'));
  let obj = {},
    M1 = d1.getMonth(),
    D1 = d1.getDate(),
    M2 = d2.getMonth(),
    D2 = d2.getDate();
  obj.Y =
    d2.getFullYear() -
    d1.getFullYear() +
    (M1 * 100 + D1 > M2 * 100 + D2 ? -1 : 0);
  obj.M = (obj.Y > 0 ? obj.Y * 12 : 12) + M2 - M1 + (D1 > D2 ? -1 : 0);
  obj.s = Math.floor((d2 - d1) / 1000); //差几秒
  obj.m = Math.floor(obj.s / 60); //差几分钟
  obj.h = Math.floor(obj.m / 60); //差几小时
  obj.D = Math.floor(obj.h / 24); //差几天
  const intervals = str.replace(/\w/g, function (a) {
    return obj[a];
  });
  console.log(obj);
  console.log(`间隔天数：${intervals}`);
  //添加日期容错
  return Math.abs(intervals) <= 3;
}

/*二代居民身份证15位转18位*/
export function getId18(idCardNo) {
  /*计算校检码*/
  function getParityBit(idCardNo) {
    const parityBit = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    const powers = [
      '7',
      '9',
      '10',
      '5',
      '8',
      '4',
      '2',
      '1',
      '6',
      '3',
      '7',
      '9',
      '10',
      '5',
      '8',
      '4',
      '2'
    ];
    let id17 = idCardNo.substring(0, 17);
    /*加权 */
    let power = 0;
    for (let i = 0; i < 17; i++) {
      power += parseInt(id17.charAt(i), 10) * parseInt(powers[i]);
    }
    /*取模*/
    let mod = power % 11;
    return parityBit[mod];
  }
  if (idCardNo.length === 15) {
    let id17 = idCardNo.substring(0, 6) + '19' + idCardNo.substring(6);
    let parityBit = getParityBit(id17);
    return id17 + parityBit;
  } else {
    return idCardNo;
  }
}

export function dataURLtoBlob(dataurl) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  let blob = new Blob([u8arr], { type: mime });
  blob.name = new Date().getTime() + '.' + mime.split('/')[1];
  return blob;
}

export function fileToBase64(file) {
  console.log('进入fileToBase64方法')
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export function uploadVideo({
  url,
  param,
  listner = {
    success: () => {},
    error: () => {},
    progress: () => {}
  }
}) {
  var xhr = new XMLHttpRequest();
  xhr.onreadystatechange = function () {
    if (xhr.readyState === 4 && 200 === xhr.status) {
      try {
        listner.success(JSON.parse(xhr.responseText));
      } catch (e) {
        console.log(e);
        // _hvueToast({ mes: '上传失败' });
      }
    }
  };
  if (xhr.upload) {
    // 	Data transfer is going on
    xhr.upload.addEventListener('progress', function (e) {
      var p = (e.loaded / e.total) * 100;
      listner.progress(p.toFixed(0));
    });
    // The fetch starts
    xhr.upload.addEventListener('loadstart', function (e) {
      console.log(e);
    });
    // The fetch operation didn't complete by the timeout the author specified
    xhr.upload.addEventListener('timeout', function (e) {
      console.log(e);
      listner.error(e);
    });
    // The fetch failed
    xhr.upload.addEventListener('error', function (e) {
      console.log(e);
      listner.error(e);
    });
    // The fetch operation was aborted
    xhr.upload.addEventListener('abort', function (e) {
      console.log(e);
      listner.error(e);
    });
  } else {
    listner.error(new Error('不支持进度条....'));
  }
  xhr.open('post', url, true);
  xhr.setRequestHeader(
    'Accept',
    'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9'
  );
  xhr.setRequestHeader('Content-Type', 'application/json');
  xhr.setRequestHeader('Authorization', $h.getSession('authorization'));
  xhr.setRequestHeader(
    'tk-token-authorization',
    $h.getSession('authorization')
  );
  xhr.setRequestHeader('tk-jwt-authorization', $h.getSession('jwtToken'));
  xhr.setRequestHeader('tk-flow-token', $h.getSession('TKFlowToken'));
  xhr.setRequestHeader('x-bus-id', $h.getSession('bizType'));
  xhr.setRequestHeader('merchantId', $hvue.customConfig.merchantId);
  try {
    xhr.send(JSON.stringify(param));
  } catch (e) {
    listner.error(e);
  }
  return xhr;
}

export function execQQStockJSBridge(fn) {
  if (typeof window.StockJSBridge === 'undefined') {
    if (document.addEventListener) {
      document.addEventListener('StockJSBridgeReady', fn, false);
    } else if (document.attachEvent) {
      document.attachEvent('StockJSBridgeReady', fn);
      document.attachEvent('onStockJSBridgeReady', fn);
    }
  } else {
    // 安卓开发反馈客户端注入的时间可能比js执行的早
    // 或者sdk在调用之前已经初始化好了
    fn();
  }
}
// 数组内部去重
export function uniqueObjectsArray(arr) {
  const uniqueArray = [];
  const seenObjects = new Set();
  for (const obj of arr) {
    // 将对象转换为 JSON 字符串以进行比较
    const objString = JSON.stringify(obj);
    // 检查是否已经存在于集合中
    if (!seenObjects.has(objString)) {
      seenObjects.add(objString);
      uniqueArray.push(obj);
    }
  }
  return uniqueArray;
}

/**
 * 跳转第三方地址
 * @param {String} url 跳转地址
 */
export function jumpThirdPartyUrl({ bizType, url, fullScreen }) {
  const pageData =
    $hvue.customConfig?.tkBizTypeList.filter(
      (it) => it.bizType === bizType
    )[0] || {};
  if (pageData.bizType) {
    if (pageData.pageName) {
      window.$router.push({
        name: pageData.pageName,
        query: {
          routerBack: 'true'
        }
      });
    } else {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: pageData.bizType });
      });
    }
    return;
  }
  if ($hvue.platform === '0') {
    window.location.href = url;
  } else {
    console.log(setSsoLoginCache);
    setSsoLoginCache({
      authorization: $h.getSession('authorization'),
      userInfo: window?.$router.app.$store.state.user.userInfo
    });
    let reqParams = {
      funcNo: '60099',
      moduleName: $hvue.customConfig.moduleName,
      actionType: '6',
      params: {
        url,
        fullScreen,
        leftType: 1,
        rightType: 99,
        rightText: ''
      }
    };
    console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
    const res = $h.callMessageNative(reqParams);
    console.log(`请求结果为: ~~${JSON.stringify(res)}`);
  }
}

function validateOperator(op) {
  var allowedOperators = ['>', '>=', '=', '<', '<='];
  if (typeof op !== 'string') {
    throw new TypeError(
      'Invalid operator type, expected string but got ' + typeof op
    );
  }
  if (allowedOperators.indexOf(op) === -1) {
    throw new TypeError(
      'Invalid operator, expected one of ' + allowedOperators.join('|')
    );
  }
}

function validate(version) {
  var semver =
    /^v?(?:\d+)(\.(?:[x*]|\d+)(\.(?:[x*]|\d+)(\.(?:[x*]|\d+))?(?:-[\da-z\-]+(?:\.[\da-z\-]+)*)?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i;
  if (typeof version !== 'string') {
    throw new TypeError('Invalid argument expected string');
  }
  if (!semver.test(version)) {
    throw new Error(
      "Invalid argument not valid semver ('" + version + "' received)"
    );
  }
}

function indexOrEnd(str, q) {
  5;
  return str.indexOf(q) === -1 ? str.length : str.indexOf(q);
}

function split(v) {
  var c = v.replace(/^v/, '').replace(/\+.*$/, '');
  var patchIndex = indexOrEnd(c, '-');
  var arr = c.substring(0, patchIndex).split('.');
  arr.push(c.substring(patchIndex + 1));
  return arr;
}

function compareVersions(v1, v2) {
  [v1, v2].forEach(validate);

  var s1 = split(v1);
  var s2 = split(v2);

  for (var i = 0; i < Math.max(s1.length - 1, s2.length - 1); i++) {
    var n1 = parseInt(s1[i] || 0, 10);
    var n2 = parseInt(s2[i] || 0, 10);

    if (n1 > n2) return 1;
    if (n2 > n1) return -1;
  }

  var sp1 = s1[s1.length - 1];
  var sp2 = s2[s2.length - 1];

  if (sp1 && sp2) {
    var p1 = sp1.split('.').map(tryParse);
    var p2 = sp2.split('.').map(tryParse);

    for (i = 0; i < Math.max(p1.length, p2.length); i++) {
      if (
        p1[i] === undefined ||
        (typeof p2[i] === 'string' && typeof p1[i] === 'number')
      )
        return -1;
      if (
        p2[i] === undefined ||
        (typeof p1[i] === 'string' && typeof p2[i] === 'number')
      )
        return 1;

      if (p1[i] > p2[i]) return 1;
      if (p2[i] > p1[i]) return -1;
    }
  } else if (sp1 || sp2) {
    return sp1 ? -1 : 1;
  }

  return 0;
}

export function getIOSVersion() {
  try {
    var match = navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);
    if (match == null) {
      return '0';
    }
    var version = [
      parseInt(match[1], 10),
      parseInt(match[2], 10),
      parseInt(match[3] || 0, 10)
    ];
    return version.join('.');
  } catch (e) {
    return '0';
  }
}

export function compareVersionsCompare(v1, v2, operator) {
  validateOperator(operator);

  switch (operator) {
    case '>':
      return compareVersions(v1, v2) > 0;
    case '>=':
      return compareVersions(v1, v2) >= 0;
    case '<':
      return compareVersions(v1, v2) < 0;
    case '<=':
      return compareVersions(v1, v2) <= 0;
    default:
      // Since validateOperator already checks the operator, this case in the switch checks for the '=' operator
      return compareVersions(v1, v2) === 0;
  }
}

export function isWeixin() {
  var ua = navigator.userAgent.toLowerCase();
  if (ua.match(/MicroMessenger/i) == 'micromessenger') {
    return true;
  } else {
    return false;
  }
}

//保存会话信息到原生缓存中
export function setSsoLoginCache(data) {
  const reqParam = {
    funcNo: '50040',
    key: 'ssoLoginToken',
    value: JSON.stringify(data)
  };
  const results50040 = $h.callMessageNative(reqParam);
  console.log(`50040 callback ********`);
  console.log(results50040);
}

//删除会话信息到原生缓存中
export function removeSsoLoginCache() {
  const reqParam = {
    funcNo: '50040',
    key: 'ssoLoginToken',
    value: ''
  };
  const results50040 = $h.callMessageNative(reqParam);
  console.log(`50040 callback ********`);
  console.log(results50040);
}

//获取原生缓存中的会话信息
export function getSsoLoginCache() {
  const reqParam50041 = {
    funcNo: '50041',
    key: 'ssoLoginToken'
  };
  const results50041 = $h.callMessageNative(reqParam50041);
  console.log(`50041 callback ********`);
  console.log(results50041);
  if (
    results50041 &&
    results50041.results &&
    results50041.results.length !== 0 &&
    results50041.results[0].value !== ''
  ) {
    const { authorization, userInfo } = results50041.results[0].value;
    if (!authorization || !userInfo) return null;
    removeSsoLoginCache();
    return {
      data: userInfo,
      responseHeaders: {
        'tk-token-authorization': authorization
      }
    };
  }
  return null;
}

/* 获取浏览器版本 */
export function checkedUserAgent() {
  const u = navigator.userAgent;
  return {
    //移动终端浏览器版本信息
    trident: u.indexOf('Trident') > -1, //IE内核
    presto: u.indexOf('Presto') > -1, //opera内核
    webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
    mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/), //是否为移动终端
    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或者uc浏览器
    iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1, //是否为iPhone或者QQHD浏览器
    iPad: u.indexOf('iPad') > -1, //是否iPad
    webApp: u.indexOf('Safari') == -1, //是否web应该程序，没有头部与底部
    weixin: u.indexOf('MicroMessenger') > -1, //是否为微信浏览器
    harmony: /(.*?harmony.*?arkweb.*?yjb)/i.test(u) //是否为佣金宝鸿蒙端
  };
}
// 向连接中添加参数
export function addUrlParam(url, param, value) {
  if (!value) {
    return url;
  }
  if (/\?/g.test(url)) {
    let originUrl = url.split('?')[0];
    let urlObj = new URL(url);
    let params = new URLSearchParams(urlObj.search.slice(1));
    if (params.has(param)) {
      params.set(param, value);
    } else {
      params.append(param, value);
    }
    url = originUrl + '?' + params.toString();
  } else {
    url += '?' + param + '=' + value;
  }
  return url;
}

//对接登录服务，获取渠道token
export function getInstantToken() {
  return new Promise((resolve, reject) => {
    let { appId, userInfo } = vm.$store.state.user;
    appId = appId || 'yjbweb';
    const opStation =  sessionStorage.getItem('originOpStation');
    const accountType = $h.getSession('accountType') === '1' ? '10' : '1';
    instantTokenGen({
      appId,
      account: userInfo.fundAccount,
      accountType //1普通资金账号 10信用资金账号
    })
      .then(({ data, code, msg }) => {
        if (code === 0) {
          const instantToken = data.instantToken;
          resolve({
            instantToken,
            opStation,
            appId
          });
        } else {
          reject(msg);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
}
export function locationReplace(url) {
  var channel_type = $h.getSession('channelType') || '';
  if ($hvue.platform === '0' && channel_type == '*************') {
    if (history.replaceState) {
      history.replaceState(null, document.title, url);
      history.go(0);
    } else {
      location.replace(url);
    }
  } else {
    location.replace(url);
  }
}

function accMultiply(num, value) {
  let m = 0;
  const s1 = num.toString();
  const s2 = value.toString();
  try {
    m += s1.split('.')[1].length;
  } catch (e) {
    console.log(e);
  }
  try {
    m += s2.split('.')[1].length;
  } catch (e) {
    console.log(e);
  }
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
}

export function toFixedMoneyFormat(value, fixed = 2) {
  if (fixed > 20 || fixed < 0) throw new RangeError('toFixedMoneyFormat：有效数字只能指定0-20之间');
  let num = 0;
  try {
    num = parseFloat(value);
    num = Math.floor(accMultiply(value, Math.pow(10, fixed))) / Math.pow(10, fixed);
    num = num.toFixed(fixed);
  } catch (e) {
    return NaN;
  }

  if (!/^(\+|-)?(\d+)(\.\d+)?$/.test(num)) {
    return NaN;
  }
  let symbol = RegExp.$1;
  let intPart = RegExp.$2;
  let decimalPart = RegExp.$3;
  const re = /(\d)(\d{3})(,|$)/;
  while (re.test(intPart)) {
    intPart = intPart.replace(re, '$1,$2$3');
  }
  const decimalPartLength = decimalPart.length;
  const decimalPartArray = decimalPartLength > 0 ? [decimalPart] : [];
  fixed = fixed - (decimalPartLength > 0 ? decimalPart.length - 1 : decimalPartLength);
  for (let i = 0; i < fixed; i++) {
    decimalPartArray.push('0');
  }
  return [].concat(symbol, intPart, decimalPartArray).join('');
}

export function openNewPage(pageInfo) {
  const {pageUrl, title, moduleName} = pageInfo;
  if ($hvue.platform === '0') {
    window.navigateLocationHref({ url: pageUrl });
  } else {
    let reqParams = {
      funcNo: '60099',
      // moduleName: $hvue.customConfig.moduleName,
      actionType: '6',
      params: {
        url: pageUrl,
        title,
        leftType: 1,
        rightType: 99,
        rightText: ''
      }
    };
    const res = $h.callMessageNative(reqParams);
  }
}
