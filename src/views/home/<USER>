<template>
  <section class="main fixed white_bg">
    <t-header :show-back="isApp" @back="back"></t-header>
    <article class="content">
      <div class="login_page">
        <div class="login_title">
          <h3>快速登录</h3>
          <p>密码验证通过后即可登录</p>
        </div>
        <div class="login_form">
          <div class="input_text text">
            <span class="tit">客户号</span>
            <input
              v-model="account"
              class="t1"
              type="text"
              placeholder="请输入客户号"
            />
          </div>
          <div class="input_text text">
            <span class="tit">密码</span>
            <!-- <h-keypanel
              v-model="password"
              type="tel2"
              :mask="true"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              placeholder="请输入交易密码"
            >
              <div slot="head" class="safe-head">
                <img src="@/assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel> -->
            <input
              v-model="password"
              class="t1"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入交易密码"
            />
            <a
              class="icon_eye"
              :class="showPassword ? 'show' : ''"
              @click="showPassword = !showPassword"
            ></a>
          </div>
          <div class="input_text text">
            <span class="tit">验证码</span>
            <input
              v-model="imgCode"
              class="t1"
              maxlength="4"
              type="tel"
              placeholder="请输入图形验证码"
            />
            <a class="code_img" href="javascript:void(0);" @click="imgClick">
              <img :src="imgSrc" />
            </a>
          </div>
        </div>
        <div v-show="showResetPwd" class="login_otheropea">
          <a style="" @click="forgetAccount">找回账户</a>
          <a style="" @click="resetPassword">重置密码</a>
        </div>
        <div class="ce_btn mt30">
          <a
            v-throttle
            class="p_button"
            :class="canLogin ? '' : 'disabled'"
            @click="nextClick"
            >登录</a
          >
        </div>
      </div>
    </article>
  </section>
</template>

<script>
import { login, getImgCode } from '@/service/service.js';
import { exitApp, getOpStation, getPwdEncryption, trackUuid } from '@/common/util';
import ChannelUtil from '@/common/ChannelUtil';

export default {
  name: 'Login',
  data() {
    return {
      account: '', // 账号
      password: '', // 密码
      imgCode: '', // 图片验证码
      imgSrc: '', // 图片验证码地址
      captchaToken: '',
      bizType: '',
      showPassword: false,
      showResetPwd: true,
      appCode: $h.getSession('appCode'),
      channelUtil: ''
    };
  },
  computed: {
    canLogin() {
      return !!(this.account && this.password && this.imgCode);
    },
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  created() {
    this.channelUtil = new ChannelUtil({ vm: this, appCode: this.appCode });
    if (this.channelUtil.isChannel) {
      this.channelUtil.beforeLogin();
    } else {
      $h.setSession('bizType', '');
    }
    this.imgClick();
  },
  methods: {
    back() {
      exitApp();
    },

    forgetAccount() {
      this.$router.push({ name: 'fundAccountConfirm' });
    },

    resetPassword() {
      this.$router.push({ name: 'idConfirm' });
    },

    nextClick() {
      if (!this.canLogin) {
        return;
      }
      if (!this.account) {
        _hvueToast({
          mes: '请输入客户号'
        });
        return false;
      }
      if (!this.password) {
        _hvueToast({
          mes: '请输入密码'
        });
        return false;
      }
      if (!this.imgCode) {
        _hvueToast({
          mes: '请输入验证码'
        });
        return false;
      }
      let loginParam = {
        clientId: this.account,
        tradePassword: 'encrypt:' + getPwdEncryption(this.password),
        captcha: this.imgCode,
        captchaToken: this.captchaToken,
        appCode: this.appCode,
        opStation: getOpStation()
      };
      let userInfo;
      login(loginParam)
        .then((loginData) => {
          if (loginData.code === 0) {
            let res = loginData.data;
            let authorization =
              loginData.responseHeaders['tk-token-authorization'];
            $h.setSession('authorization', authorization);
            trackUuid(res.fundAccount,'client_id');
            userInfo = Object.assign({}, res);
            this.$store.commit('user/setUserInfo', userInfo);
            if (this.channelUtil.isChannel) {
              this.channelUtil.afterLogin();
            } else if (this.$router.currentRoute.query.noHome) {
              $h.setSession('noHome', true);
              let bizType = this.$router.currentRoute.query.bizType;
              let flowNo = this.$router.currentRoute.query.flowNo;
              import('@/common/flowMixin.js').then((a) => {
                a.initFlow.call(this, bizType, flowNo);
              });
              return;
            } else {
              $h.setSession('noHome', false);
              this.$router.replace({
                name: 'home',
                query: this.$router.currentRoute.query
              });
            }
          } else if (loginData.code === 9007) {
            this.imgClick();
          } else {
            this.imgClick();
            _hvueToast({
              mes: loginData.msg
            });
          }
        })
        .catch((err) => {
          this.imgClick();
          _hvueToast({
            mes: err.msg
          });
        });
    },

    _getImgCode() {
      let _this = this;
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          _this.imgSrc = results.image;
          _this.captchaToken = results.token;

          //清空验证码
          for (let ele of _this.inputViewList) {
            if (ele.name === 'imgCode') {
              ele.value = '';
              break;
            }
          }
        }
      });
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
          this.imgCode = '';
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.login_box {
  background: #fff;
  border-radius: 0.12rem;
  margin: 0.15rem 0.12rem;
  .input_form {
    margin: 0 0.12rem;
    padding: 0.3rem 0;
    .input_text {
      // padding-left: 0.7rem;
      padding: 0.1rem 0;
      position: relative;
      box-sizing: content-box;
      min-height: 0.4rem;
      border-top: 0 none;
      border-bottom: 1px solid rgba(7, 3, 31, 0.1);
      .tit {
        font-size: 0.16rem;
        line-height: 0.4rem;
        color: rgba(2, 3, 36, 0.6);
        position: absolute;
        top: 0;
        left: 0;
      }
      .t1 {
        display: block;
        width: 100%;
        height: 0.4rem;
        padding: 0.12rem 0.12rem 0.12rem 0;
        line-height: 0.16rem;
        font-size: 0.16rem;
        border: 0 none;
        outline: none;
        background: none;
        color: rgba(7, 3, 31, 1);
        font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI',
          Roboto, Ubuntu;
        font-weight: normal;
      }
      .code_img {
        width: 0.8rem;
        height: 0.38rem;
        position: absolute;
        top: 50%;
        margin-top: -0.19rem;
        right: 0;
        z-index: 50;
      }
    }
  }
}
</style>
