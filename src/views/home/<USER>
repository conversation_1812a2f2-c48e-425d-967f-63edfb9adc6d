<template>
  <section class="main fixed white_bg" data-page="home" v-if="!loading">
    <t-header :fixed-header="true" @back="back" />
    <article class="content">
      <div class="bus_banbox">
        <div class="txt">
          <h2>{{ bizName }}</h2>
          <div class="tips">{{ bizTime }}</div>
        </div>
      </div>
      <div v-for="(it, i) in introduce" class="bus_infobox" :key="i">
        <h5 class="title"><i></i>{{ it.title }}</h5>
        <div class="cont business_content" v-html="it.content"></div>
      </div>
      <div class="txt_center" v-show="bottomLinkName !== ''">
        <a class="link_right_arrow" @click.stop="jumpPage">{{
          bottomLinkName
          }}</a>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn bome_btn">
        <a class="p_button" href="javascript:void(0);" @click="toNext">{{
          buttonName
          }}</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  queryBusinessIntroduce,
  getConfigMap,
  xhFlowInfoQry,
  businessAcceptInfoQry,
  businessProcessingProgressDetailQry
} from '@/service/service';
import { exitApp, checkedUserAgent, trackEvent } from '@/common/util';

export default {
  data() {
    return {
      loading: true,
      bizName: '',
      bizTime: '',
      bizType: this.$route.query.bizType,
      flowNo: this.$route.query.flowNo ? this.$route.query.flowNo : '',
      buttonName: '',
      xhBizType: '010046', //销户办理业务编号
      yyxhBizType: '010731', //预约销户业务编号
      xhAutoBizType: '014001', //销户自动化业务编号
      introduce: [
        {
          title: '',
          content: '',
          viewSort: 0
        }
      ],
      bottomLinkType: '', //底部链接类型：1 业务办理链接；2 非业务办理链接；
      bottomLinkName: '', //底部链接名称
      bottomLinkUrl: '', // 底部链接地址
      isHarmony: checkedUserAgent().harmony
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    },
    $route: {
      handler: function (e) {
        const { bizType } = e.query;
        if (bizType !== this.bizType) {
          this.bizType = bizType;
          this.renderingView();
        }
      },
      immediate: false
    }
  },
  created() {
    // 访至因为IOS回退或操作导致当前页面生命周期未加载
    if ($hvue.iBrowser.ios) {
      window.addEventListener('pageshow', () => {
        $h.setSession('showRejectResult', null);
        const { toPage } = this.$route.query;
        if (toPage === 'introduce' && $h.getSession('introduceNeedBack')) {
          // 处理可能重复路由栈的问题
          this.$router.back();
        }
        $h.setSession('fromBizIntroduce', true);
        if (['010039', '010040'].includes(this.bizType)) {
          this.buttonName = '申请认证';
        } else if (
          [this.yyxhBizType, this.xhBizType, this.xhAutoBizType].includes(
            this.bizType
          )
        ) {
          this.buttonName = '开始销户';
        } else {
          this.buttonName = '立即办理';
        }
      });
    }

    $h.setSession('showRejectResult', null);
    const { toPage } = this.$route.query;
    if (toPage === 'introduce' && $h.getSession('introduceNeedBack')) {
      // 处理可能重复路由栈的问题
      this.$router.back();
    }
    $h.setSession('fromBizIntroduce', true);
    if (['010039', '010040'].includes(this.bizType)) {
      this.buttonName = '申请认证';
    } else if (
      [this.yyxhBizType, this.xhBizType, this.xhAutoBizType].includes(
        this.bizType
      )
    ) {
      this.buttonName = '开始销户';
    } else {
      this.buttonName = '立即办理';
    }
  },
  mounted() {
    // 添加事件委托处理链接点击
    document.addEventListener('click', this.handleContentClick);
  },
  beforeDestroy() {
    // 组件销毁时移除事件监听
    document.removeEventListener('click', this.handleContentClick);
  },
  methods: {
    handleContentClick(event) {
      // 检查点击事件是否来自业务内容区域内的链接
      if (event.target.tagName === 'A' &&
        event.target.closest('.business_content')) {
        event.preventDefault();
        const href = event.target.getAttribute('href');
        if (href) {
          // 使用 navigateLocationHref 处理跳转
          window.navigateLocationHref({ url: href });
        }
      }
    },
    renderingView() {
      const configKey = `bc.business.accept.time.${this.bizType}`;
      const tradingDayBizType = ['010293'];
      this.introduce = [];
      this.bottomLinkType = '';
      this.bottomLinkName = '';
      this.bottomLinkUrl = '';

      queryBusinessIntroduce({
        bizType: this.bizType,
        flowNo: this.flowNo
      })
        .then((res) => {
          const {
            businessIntroduceCardList = [],
            tips = '',
            conditions = '',
            enableBottomLink = '0',
            bottomLinkType = '',
            bottomLinkName = '',
            bottomLinkUrl = ''
          } = res.data;
          this.bizName = res.data.bizName;
          trackEvent({
            event_name: 'ywbl_view',
            page_name: res.data.bizName,
            module_name: '页面展示',
            element_name: 'init'
          });
          if (businessIntroduceCardList.length === 0) {
            if (conditions !== '') {
              this.introduce.push({
                title: '申请条件',
                content: conditions,
                viewSort: 0
              });
            }
            if (tips !== '') {
              this.introduce.push({
                title: '温馨提示',
                content: tips,
                viewSort: 1
              });
            }
          } else {
            this.introduce = businessIntroduceCardList.sort(
              (a, b) => a.viewSort - b.viewSort
            );
          }
          if (enableBottomLink === '1') {
            this.bottomLinkType = bottomLinkType;
            this.bottomLinkName = bottomLinkName;
            this.bottomLinkUrl = bottomLinkUrl;
          }
          this.loading = false;
          return getConfigMap({
            configKey
          });
        })
        .then(({ data = {} }) => {
          const configInfo = data[configKey];
          if (configInfo) {
            const [start, end] = configInfo.configValue.split(',');
            if (tradingDayBizType.includes(this.bizType)) {
              this.bizTime = `办理时间：交易日${start}-${end}`;
            } else {
              this.bizTime = `办理时间：${start}-${end}`;
            }
          }
        });
    },
    back() {
      if ($hvue.platform === '0') {
        this.$router.back();
      } else {
        if ($h.getSession('history_list').index === 0) {
          exitApp();
        } else {
          this.$router.back();
        }
        /* if ($h.getSession('introduceNeedBack')) {
          this.$router.back();
        } else {
          exitApp();
        } */
      }
    },

    toNext() {
      trackEvent({
        event_name: 'ywbl_click',
        page_name: this.bizName,
        module_name: 'next',
        element_name: this.buttonName
      });

      let contextParam;
      /*  if (this.bizType && this.bizType === '010174') {
         this.$router.push({ name: 'chooseAppointmentOrTeach' });
         return;
       } */
      if (
        this.bizType &&
        ['010262', '010276', '010277'].includes(this.bizType)
      ) {
        this.$router.push({ name: 'bcPreBizList' });
        return;
      }
      if (
        this.bizType &&
        [this.yyxhBizType, this.xhBizType, this.xhAutoBizType].includes(
          this.bizType
        )
      ) {
        xhFlowInfoQry({})
          .then(({ data, code, msg }) => {
            if (code === 0) {
              //type流程类型: 0预约 1挽留处理中 2销户；
              //conclusion 挽回状态: 0挽留失败，1挽留成功，2无需挽留 3挽留处理中
              //cancelStatus 销户主流程状态,0初始化预约 1已有挽留结果 2已做双向视频 3销户办结 4发送完
              //normalOptFundAccFlag 有正常状态期权资金账户/衍生品账户  0 不存在，1存在
              const {
                type,
                bizType = '',
                conclusion = '',
                businessAccCancel = '',
                preFlowInsId = '',
                reasonAccCancel = '',
                reasonAccCancelOther = '',
                cancelStatus = '',
                reasonCancelEboss = '',
                reasonCancelOthEboss = '',
                normalOptFundAccFlag = '0', // 有正常状态期权资金账户/衍生品账户标识，1存在 0不存在（默认）
                verifMobileTel = ''
              } = data;
              /** 销户自动化需求调整：https://jira.gjzq.cn/browse/KFSBYWBLCG-5909
               * 走销户自动化流程逻辑：
               * 1. bizType==014001，表示此前已走销户自动化流程；
               * 2. type==0 && bizType!=014001 && conclusion == ''，type为0表示此前没有走完预约销户流程，conclusion为空表示没有挽留结果，所以走新流程；
               * 3. bizType!=014001 && conclusion == '1'，conclusion为1表示挽留成功。
               * 其余场景走原销户办理流程逻辑。
               *
               * 20241120 add
               * 新老兼容方案新增一条：有正常状态期权资金账户/衍生品账户，都走老流程
               * 20241120 end
               * */

              // 20241209 判断入口如是预约销户或销户办理，只走原来销户流程。
              if ([this.yyxhBizType, this.xhBizType].includes(this.bizType)) {
                if (['0'].includes(type) && conclusion === '') {
                  $h.setSession('bizType', this.yyxhBizType);
                  import('@/common/flowMixinV2.js').then((a) => {
                    a.initFlow.call(this, {
                      bizType: this.yyxhBizType,
                      flowNo: this.flowNo
                    });
                  });
                } else {
                  this.handleAccountCancellation(type, {
                    conclusion,
                    businessAccCancel,
                    preFlowInsId,
                    reasonAccCancel,
                    reasonAccCancelOther,
                    reasonCancelEboss,
                    reasonCancelOthEboss,
                    verifMobileTel
                  });
                }
                return;
              }

              if (
                ((['0'].includes(type) && conclusion === '') ||
                  bizType === this.xhAutoBizType ||
                  (bizType !== this.xhAutoBizType && conclusion === '1')) &&
                normalOptFundAccFlag !== '1'
              ) {
                $h.setSession('bizType', this.xhAutoBizType);
                let native_version = '';
                if ($hvue.platform !== '0') {
                  const result = $h.callMessageNative({
                    funcNo: '50001'
                  });
                  result.results = result.results || [];
                  let data = result.results[0];
                  native_version = data.nativeVersion;
                }
                import('@/common/flowMixinV2.js').then((a) => {
                  a.initFlow.call(this, {
                    bizType: this.xhAutoBizType,
                    flowNo: this.flowNo,
                    initJumpMode: '0',
                    contextParam: JSON.stringify({
                      origin: $hvue.platform,
                      native_version
                    })
                  });
                });
              } else if (
                normalOptFundAccFlag === '1' &&
                ['0'].includes(type) &&
                conclusion === ''
              ) {
                $h.setSession('bizType', this.yyxhBizType);
                import('@/common/flowMixinV2.js').then((a) => {
                  a.initFlow.call(this, {
                    bizType: this.yyxhBizType,
                    flowNo: this.flowNo,
                    initJumpMode: '0'
                  });
                });
              } else {
                this.handleAccountCancellation(type, {
                  conclusion,
                  businessAccCancel,
                  preFlowInsId,
                  reasonAccCancel,
                  reasonAccCancelOther,
                  reasonCancelEboss,
                  reasonCancelOthEboss
                });
              }
            } else {
              return Promise.reject(msg);
            }
          })
          .catch((err) => {
            this.$TAlert({
              tips: err
            });
          });
        return;
      }
      if (['010061'].includes(this.bizType)) {
        const { subBizType } = this.$route.query; //1 新开；2 转签；3 补签
        contextParam = JSON.stringify({
          subBizType
        });
      }
      if (this.bizType) {
        $h.setSession('bizType', this.bizType);
        if (this.flowNo) {
          import('@/common/flowMixinV2.js').then((a) => {
            a.initFlow.call(this, {
              bizType: this.bizType,
              flowNo: this.flowNo,
              initJumpMode: '0',
              contextParam
            });
          });
        } else {
          let bizType =
            (this.$route.query && this.$route.query.bizType) ||
            $hvue.customConfig.bizType;
          import('@/common/flowMixinV2.js').then((a) => {
            a.initFlow.call(this, {
              bizType,
              flowNo: this.flowNo,
              initJumpMode: '0',
              contextParam
            });
          });
        }
      }
    },
    /**
     * @description 查询销户办理流程状态
     */
    async handleAccountCancellation(type, { ...params }) {
      try {
        const { code, data } = await businessAcceptInfoQry({
          bizType: this.xhBizType
        });
        if (code === 0) {
          let isReset = true;
          //找到最新一笔受理单，用受理单id去查询表单字段preFlowInsId，如果与预约销户id一致（并且受理单状态不为办理成功），表示此前无需新开流程，否则强制新开流程
          if (data.flowInsList.length > 0) {
            const { data: flowInfo } =
              await businessProcessingProgressDetailQry({
                flowInsId: data.flowInsList[0].id
              });
            console.log(flowInfo);
            if (flowInfo.data.preFlowInsId !== params.preFlowInsId) {
              //销户办理此前没有绑定的预约销户受理单
              isReset = true;
            } else {
              isReset =
                flowInfo.data.preFlowInsId === params.preFlowInsId &&
                ['3'].includes(data.flowInsList[0].status);
            }
          }
          // 进入预约销户结果页逻辑
          if (
            ['1'].includes(type) || //挽留处理中
            (['0'].includes(type) && ['1'].includes(params.conclusion)) || // 挽留成功
            (['0'].includes(type) &&
              ['0', '3'].includes(params.conclusion) &&
              isReset) || // 挽留失败或者挽留处理中
            (['2'].includes(type) && isReset) //已进入销户办理
          ) {
            const { app_id = '' } = this.$route.query;
            this.$router.push({
              name: 'yyxhResult',
              query: { app_id }
            });
          } else if (params.preFlowInsId === '') {
            return Promise.reject('未能查询到预约销户信息');
          } else {
            let contextParam = JSON.stringify(params);
            $h.setSession('bizType', this.xhBizType);
            import('@/common/flowMixinV2.js').then((a) => {
              a.initFlow.call(this, {
                bizType: this.xhBizType,
                isReset,
                contextParam
              });
            });
          }
        }
      } catch (error) {
        this.$TAlert({
          tips: error
        });
      }
    },
    jumpPage() {
      /* 跳转规则
       *  bottomLinkType = 1 业务办理链接
       *  - bottomLinkUrl是6位数字，判断为业务编号，进行流程初始化
       *  - bottomLinkUrl是英文单词，判断为页面路由名称，使用$router路由跳转
       *  - bottomLinkUrl是URL链接，但是没有拼接证书域名端口，使用当前地址拼接跳转
       *  - bottomLinkUrl是完整URL链接，直接跳转
       */
      if (this.bottomLinkType === '2') {
        if ($hvue.platform === '0') {
          window.navigateLocationHref({ url: this.bottomLinkUrl });
        } else {
          let reqParams = {
            funcNo: '60099',
            moduleName: $hvue.customConfig.moduleName,
            actionType: '6',
            params: {
              url: this.bottomLinkUrl,
              leftType: 1,
              rightType: 99,
              rightText: ''
            }
          };
          console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
          const res = $h.callMessageNative(reqParams);
          console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        }
      } else if (this.bottomLinkType === '1') {
        // 内部跳转处理
        if (/^\d{6}$/.test(this.bottomLinkUrl)) {
          $h.setSession('bizType', this.bottomLinkUrl);
          import('@/common/flowMixinV2.js').then((a) => {
            a.initFlow.call(this, {
              bizType: this.bottomLinkUrl,
              initJumpMode: '0'
            });
          });
        } else if (/^[a-zA-Z0-9_]+$/.test(this.bottomLinkUrl)) {
          // 英文单词，判断为页面路由名称
          this.$router.push({
            name: this.bottomLinkUrl
          });
        } else if (
          this.bottomLinkUrl.startsWith('/') ||
          !this.bottomLinkUrl.includes('://')
        ) {
          // URL链接，但没有域名，使用当前地址拼接
          const currentOrigin = window.location.origin;
          const fullUrl = this.bottomLinkUrl.startsWith('/')
            ? `${currentOrigin}${this.bottomLinkUrl}`
            : `${currentOrigin}/${this.bottomLinkUrl}`;
          window.navigateLocationHref({ url: fullUrl });
        } else {
          // 完整URL链接，直接跳转
          window.navigateLocationHref({ url: this.bottomLinkUrl });
        }
      }
    }
  }
};
</script>
<style scoped>
div.cont>>>ul {
  padding-left: 0.05rem;
  padding-bottom: 0rem;
}

div.cont>>>ul>li {
  list-style-type: disc;
  padding: 0.05rem 0 0.05rem 0.05rem;
}

div.cont>>>p {
  padding-left: 0.05rem;
}

div.cont>>>ul>li>strong {
  position: relative;
  font-size: 0.16rem;
  font-weight: 500;
  line-height: 1.375;
  color: #333333;
}
</style>
