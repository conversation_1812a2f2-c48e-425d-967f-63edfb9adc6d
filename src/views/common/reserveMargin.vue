<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back"></t-header>
    <article class="content">
      <div><img class="pic" src="@/assets/images/reserveMargin/icon_intro.png" /></div>
      <div><img class="pic" src="@/assets/images/reserveMargin/icon_advan.png" /></div>
      <div class="item-title">
        <img class="pics" src="@/assets/images/reserveMargin/icon_con.png" className="iconCon" />
      </div>
      <div class="item-content">
        <div class="item-text">客户已经在国金证券开通股东账户，且已绑定三方存管银行;</div>
        <div class="item3-button-wrapper" @click="toKaihu">
          <div class="item3-button">
            若没有国金证券股东账户，点此开户<span className="pad5">{{ '>' }}</span>
          </div>
        </div>
        <div class="item-text">在全市场交易满6个月；</div>
        <div class="item-text">在我司开立的账户内最近20个交易日日均证券类资产在50万以上(含50万元);</div>
        <div class="item-text">
          客户具备一定投资经验和相当的风险承受能力（融资融券业务为R4中高风险等级），且无重大的违约记录等情形;
        </div>
      </div>
      <div class="item5">
        <div class="item-title">
          <img src='@/assets/images/reserveMargin/icon_process.png' class="iconProcess" />
        </div>
        <div class="item-content">
          您可以在此留下手机号预约两融开户，国金证券工作人员会在1个工作日内联系您办理业务。
        </div>
      </div>
    </article>

    <footer class="footer">
      <div class="ce_btn bome_btn">
        <a class="p_button" href="javascript:void(0);" @click="toReserve">立即预约</a>
      </div>
    </footer>
    <van-popup v-model="showIdKind" round position="bottom">
      <div class="layer_tit">
        <h3>请填写预约手机号</h3>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">手机号</span>
            <input v-model="phone" placeholder="请输入11位手机号" ref="phoneNumInput" class="t1" type="tel" maxlength="11" />
          </div>
          <div class="input_text text code">
            <span class="tit">图形码</span>
            <input v-model="captcha" class="t1" type="text" maxlength="4" placeholder="请输入图形码" />
            <a class="code_img" @click="imgClick"><img :src="imgSrc" /></a>
          </div>
          <div class="input_text text code">
            <span class="tit active">验证码</span>
            <input id="smsCode" v-model="smsCode" @input="smsCode = smsCode.replace(/[^\d]/g, '')" class="t1" type="tel"
              maxlength="6" placeholder="请输入短信验证码" autocomplete="off" />
            <sms-code-btn-for-margin v-model="uuid" :need-img-code="false" :mobile-no="phone" biz-type="010200"
              :captcha="captcha" :captcha-token="captchaToken" @send-result="SMSCodeCallback" :sendBefore="checkPhone"
              @change="SMSCodeChange" />
          </div>
        </div>
        <div class="ce_btn bome_btn">
          <a :class="ispass ? 'p_button' : 'p_button p_button_disable'" href="javascript:void(0);"
            @click="bookNow">立即预约</a>
        </div>
      </div>
    </van-popup>
  </section>
</template>

<script>
import SmsCodeBtnForMargin from '@/components/SmsCodeBtnForMargin.vue';
import { saveActivity, getConfigMap, getImgCode, detainPredicate, queryJDMobileInfo } from '@/service/service';
import { formatMobileNo } from '@/common/filter';
import { openNewPage, getInstantToken } from '@/common/util';
import { mapGetters } from 'vuex';


export default {
  components: {
    SmsCodeBtnForMargin
  },
  data() {
    return {
      gtPhone: '',
      phone: '',
      smsCode: '',
      imgSrc: '',
      uuid: '',
      showIdKind: false,
      // desensitize: true //是否手机号脱敏

      captcha: '',
      captchaToken: '',
      alertFlag: false,
      channelList: []
    };
  },
  mounted() {
    this.captcha = '';
    this.captchaToken = '';
    this.uuid = '';
    // this.imgClick();
    this.getChannelList();
    this.queryStatus();
    //京东定制逻辑
    const params = new URLSearchParams(window.location.search);
    if (params.get('channel_type') == '*************' || params.get('channel_type') == '*************') {
      this.queryAccountInfo();
    }

  },
  computed: {
    ...mapGetters('user', ['userInfo']),
    isApp() {
      return $hvue.platform !== '0';
    },
    ispass() {
      return this.phone != '' && this.smsCode != '' && this.captcha != ''
    },
    phoneNumInput: {
      get() {
        if (this.desensitize) {
          return formatMobileNo(this.phone);
        } else {
          return this.phone;
        }
      },
      set(val) {
        this.phone = val;
      }
    },
  },
  methods: {
    back() {
      // 检测是否弹框
      console.log("返回")
      if (this.alertFlag) {
        this.$TAlert({
          title: '温馨提示',
          tipsHtml: '您的<span style="color:#F93838;">日均资产</span>及<span style="color:#F93838;">交易经验</span>已满足两融信用账户办理条件，确认离开吗?',
          hasCancel: true,
          cancelBtn: '残忍离开',
          confirmBtn: '预约开户',
          cancel: () => { this.$router.back(); },
          confirm: () => { this.toReserve() }
        });
      } else {
        // 直接返回
        this.$router.back();
      }
    },
    // 获取配置的渠道
    getChannelList() {
      let key = 'bc.010277.special.channels';
      getConfigMap({
        configKey: key
      }).then((res) => {
        this.channelList = res.data[key].configValue.split(',');
        console.log('this.channelList=====', res.data[key].configValue.split(','))
      });
    },
    // 京东openId处理
    queryAccountInfo() {
      const params = new URLSearchParams(window.location.search);

      queryJDMobileInfo({
        openId: params.get('openid') || null,
        sourceType: params.get('sourcetype') || null,
        ticket: params.get('ticket') || null
      }, { loading: false }).then(res => {
        if (res.code == 0) {
          this.phone = res.data.jrAppNumber
        }
      })
    },
    queryStatus() {
      // 查询是否弹框提醒
      detainPredicate({}, { loading: false }).then(res => {
        if (res.code == 0 && res.data.popFlag == '1') {
          this.alertFlag = true;
        } else {
          this.alertFlag = false;
        }
      })
    },
    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.captcha = '';
          this.imgSrc = results.image;
          this.captchaToken = results.token;
        }
      });
    },
    // checkPhone() {
    //   return true
    // },
    checkPhone() {
      // let arrs = JSON.parse(this.$route.query.mobileTel);
      let arrs = JSON.parse($h.getSession('mobileTel'));
      if (!this.phone) {
        this.$TAlert({
          tips: '请输入手机号'
        });
        return false;
      }
      if (!this.captcha) {
        _hvueToast({
          mes: '请输入图形验证码'
        });
        // this.$emit('send-result', false);
        return false;
      }
      if (this.captcha.length < 4) {
        _hvueToast({
          mes: '图形验证码格式不正确'
        });
        // this.$emit('send-result', false);
        return false;
      }
      return true
    },
    SMSCodeCallback(flag) {
      if (!flag) {
        this.uuid = '';
        this.imgClick();
      }
    },
    SMSCodeChange(uuid) {
      console.log('serialNumber===', uuid)
      this.uuid = uuid
    },
    // 格式化手机
    formatTel(tel) {
      const reg = /^(\d{3})\d{4}(\d{4})$/;
      return tel.replace(reg, '$1****$2');
    },
    bookNow() {
      if (!this.ispass) {
        _hvueToast({
          mes: '请输入手机号和验证码'
        });
        return;
      }
      let channelLists = this.channelList;
      const params = new URLSearchParams(window.location.search);
      let channelType = params.get('channel_type') || $h.getSession('channelType');

      // 校验手机号，验证码，并提交接口
      saveActivity({
        mobileNo: this.phone,
        serialNumber: this.uuid,
        accountType: 1,
        activityNo: params.get('activity_no') || $h.getSession('activity_no'),
        channelType: params.get('channel_type') || $h.getSession('channelType'),
        terminalType: params.get('terminal_type') || $h.getSession('terminal_type'),
        // template_no: 35,
        accountContent: '',
        captchaCode: this.smsCode,
        remark: location.search
      }, { filter: true }).then(res => {
        let data = res.data;
        if (res.code == 1040004) {
          _hvueToast({
            mes: '验证码不正确，请重新输入验证码'
          });
          this.imgClick();
          return;
        }
        if (res.code == 1000) {
          _hvueToast({
            mes: res.msg
          });
          return;
        }
        this.showIdKind = false;// 关闭浮框
        // 清理表单
        this.smsCode = ''
        if (res.code == 0) {
          this.alertFlag = false;
          if ($hvue.platform !== '0' || channelLists.includes(channelType)) {
            this.alertTips(true, '您的预约已提交', '国金证券专属服务人员将于工作日<span style="color:#F93838;">9:00-18:00</span>与您联系，请保持手机畅通。<span style="color:#F93838;">添加服务人员企微</span>，备注【两融开户+姓名】，随时随地了解开户进度。')
          } else {
            this.alertTips(false, '您的预约已提交', '国金证券专属服务人员将于工作日<span style="color:#F93838;">9:00-18:00</span>与您联系，请保持手机畅通。')
          }
        }
        if (res.code == 2690002) {
          this.alertFlag = false;
          if ($hvue.platform !== '0' || channelLists.includes(channelType)) {
            this.alertTips(true, '温馨提示', '您已预约过两融开户，请耐心等待国金证券工作人员与您联系。若需实时掌握开户进度，可<span style="color:#F93838;">添加服务人员</span>，备注【两融开户+姓名】咨询');
          } else {
            this.alertTips(false, '温馨提示', '您已预约过两融开户，请耐心等待国金证券工作人员与您联系。');
          }
        }
        if (res.code == 2020018) {
          this.alertFlag = false;
          this.$TAlert({
            title: '温馨提示',
            tipsHtml: '尊敬的客户您好，您的预约申请已提交，您的手机号<span style="color:#F93838;">' + this.formatTel(this.phone) + '</span>尚未在国金证券开立资金账户，建议您前往开户哦。',
            hasCancel: true,
            cancelBtn: '我知道了',
            confirmBtn: '去开户',
            cancel: () => { },
            confirm: () => {
              // 跳转开户链接
              this.toKaihu();
            }
          });
        }

      }).catch(msg => {
        _hvueToast({
          mes: msg
        });
      })
    },
    // 去开户
    toKaihu() {
      let href = serviceOptions.financingFacilityServer + '/yjbwxkh/openstock/web/index.html';
      let querystring = window.location.search;
      href = href + querystring.replace("app_id=yjbweb", "app_id=web");
      if ($hvue.platform !== '0') {
        href = href + '?app_id=yjb3.0'
      }
      openNewPage({
        pageUrl: href,
        title: '开户',
      })
    },
    // 立即预约
    toReserve() {
      this.showIdKind = true
      this.imgClick();
    },
    // 弹框提示
    alertTips(hasWx, title, tips) {
      if (hasWx) {
        this.$TAlert({
          title: title,
          tipsHtml: tips,
          hasCancel: true,
          cancelBtn: '我知道了',
          confirmBtn: '加企微咨询',
          cancel: () => { },
          confirm: async () => {
            let pageUrl = serviceOptions.target + '/ims/guide-to-add-qywechat/index?source=102&scene=205_000009';
            // 跳转到企微  
            if ($hvue.platform == '0') {
              try {
                const { instantToken } = await getInstantToken();
                pageUrl = pageUrl + '&instant_token=' + instantToken;
              } catch (e) {
                console.log(e)
              }
            }
            openNewPage({
              pageUrl,
              title: '添加企业微信',
            })
          }
        });
      } else {
        this.$TAlert({
          title: title,
          tipsHtml: tips,
          confirmBtn: '我知道了',
        });
      }
    }
  },
}
</script>

<style scoped lang="less">
/deep/.van-overlay {
  z-index: 100 !important;
}

/deep/.van-popup--bottom {
  z-index: 100 !important;
}

.pic {
  width: 100%;
}

.layer_tit {
  position: relative;
  text-align: center;
  height: 3rem;
  /* line-height: rem; */
}

.item-title {
  display: flex;
  justify-content: center;
  margin: 20px 0;

  img {
    width: 50%;
  }
}

.item-content {
  font-size: 15px;
  padding: 0 15px;
}

.item3-button-wrapper {
  width: 100%;
  padding: 15px 0;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
}

.item3-button {
  width: 280px;
  height: 40px;
  font-weight: bold;
  line-height: 40px;
  vertical-align: middle;
  background: linear-gradient(180deg, #B8C7FE 0%, #6FA0FA 100%);
  box-shadow: 5px 0px 47px 0px rgba(61, 136, 236, 0.34);
  border-radius: 39px;
  border: 1px solid #FFFFFF;
  text-align: center;
  color: #FFF;
}

.item-content .item-text {
  padding-left: 30px;
  padding-bottom: 10px;
  line-height: 30px;
}

.item-content .item-text:nth-child(1) {
  background-image: url('../../assets/images/reserveMargin/icon_one.png');
  background-repeat: no-repeat;
  background-position: left top;
  background-size: auto 30px;
}

.item-content .item-text:nth-child(3) {
  background-image: url('../../assets/images/reserveMargin/icon_two.png');
  background-repeat: no-repeat;
  background-position: left top;
  background-size: auto 30px;
}

.item-content .item-text:nth-child(4) {
  background-image: url('../../assets/images/reserveMargin/icon_three.png');
  background-repeat: no-repeat;
  background-position: left top;
  background-size: auto 30px;
}

.item-content .item-text:nth-child(5) {
  background-image: url('../../assets/images/reserveMargin/icon_four.png');
  background-repeat: no-repeat;
  background-position: left top;
  background-size: auto 30px;
}

.item5 .item-title {
  width: 100%;
  padding: 24px 0;
  text-align: center;
}

.item5 .item-title img {
  width: 103px;
  height: 37px;
}

.item5 .item-content {
  width: 100%;
  padding: 0 20px 40px;
  font-size: 15px;
  color: #333333;
}

.item5 .item-content img {
  width: 100%;
}

.p_button {
  background: #3D88EC;

}

.p_button_disable {
  background: gray !important;
}
</style>
