<template>
  <section class="main fixed white_bg" data-page="home">
    <t-header @back="back" />
    <article class="content">
      <div class="info_compage">
        <div class="com_title">
          <h5>为了您的账号安全，请正确填写以下信息</h5>
        </div>
        <div class="input_form">
          <div class="input_text text">
            <span class="tit active">资金账号</span>
            <input
              v-model.trim="fundAccount"
              class="t1"
              type="tel"
              placeholder="请输入资金账号"
            />
            <a class="code_btn" @click="jumpPage">找回账号</a>
          </div>
          <div class="input_text text">
            <span class="tit active">证件类型</span>
            <div class="dropdown" @click="showIdTypeMap = true">
              {{ idTypeName }}
            </div>
          </div>
          <div class="input_text text">
            <span class="tit active">证件号码</span>
            <input
              v-model.trim="idNo"
              class="t1"
              type="text"
              placeholder="请输入证件号码"
            />
          </div>
          <div class="input_text text code">
            <span class="tit active">图形验证码</span>
            <input
              v-model.trim="imgCode"
              class="t1"
              maxlength="4"
              type="tel"
              placeholder="请输入图形验证码"
            />
            <a class="code_img" @click="imgClick">
              <img :src="imgSrc" />
            </a>
          </div>
        </div>
      </div>
      <div class="rule_check" @click="isChecked = !isChecked">
        请勾选并同意国金证券根据监管要求，收集您的身份信息用于忘记密码，我们将依法保护您的隐私安全。
        <span class="icon_check" :class="{ checked: isChecked }"></span>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a class="p_button" @click="checkInput">下一步</a>
      </div>
    </footer>
    <van-popup v-model="showIdTypeMap" round position="bottom">
      <v-picker
        v-model="idType"
        :columns="idTypeMap"
        @onConfirm="pickerCallback"
        @onCancel="showIdTypeMap = false"
      />
    </van-popup>
  </section>
</template>

<script>
import VPicker from '@/components/VPicker';
import { ID_KIND } from '@/common/enumeration';
import { resetPasswordAuth, getImgCode } from '@/service/service.js';
import { exitApp, getOpStation } from '@/common/util';
import AlipayUtil from '@/common/AlipayUtil';
import { nativeFunc60094 } from '@/nativeShell/h5CallNative';

export default {
  components: {
    VPicker
  },
  data() {
    return {
      fundAccount: '',
      idType: '',
      idTypeName: '',
      showIdTypeMap: false,
      idTypeMap: [
        { label: '二代身份证', value: ID_KIND.PERSONAL_ID },
        { label: '港澳台居民居住证', value: ID_KIND.HK_MACAU_TAIWAN_ID },
        { label: '港澳台居民来往内地通行证', value: ID_KIND.HK_MACAU_PASS }
      ],
      idNo: '',
      imgCode: '',
      imgSrc: '',
      captchaToken: '',
      bizType: '010208',
      opStation: '',
      isChecked: false
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  created() {
    nativeFunc60094({
      isInterceptScreenshot: '1',
      screenCaptureTip: '请妥善保存您的账号及密码，您保存的图片内容可能涉及到敏感信息，请请勿发送给他人'
    });
    document.addEventListener('visibilitychange', this.viewHideActivate);

    this.opStation = this.$route.query.op_station || '';
    this.imgClick();
    this.idType = this.idTypeMap[0].value;
    this.idTypeName = this.idTypeMap[0].label;
    const alipayUtil = new AlipayUtil();
    if (alipayUtil.checkAlipay) alipayUtil.setTitle(this.$route.meta.title);
  },
  destroyed() {
    nativeFunc60094({
      isInterceptScreenshot: '0'
    });
    document.removeEventListener('visibilitychange', this.viewHideActivate);
  },
  methods: {
    viewHideActivate() {
      if (document.visibilityState === 'hidden') {
        // 页面进入后台时执行的代码
        this.fundAccount = '';
        this.idNo = '';
        this.imgCode = '';
        console.log('页面进入后台');
      }
    },
    back() {
      this.$store.commit('user/setUserInfo', null);
      localStorage.removeItem('vuex');
      sessionStorage.clear();
      if (this.isApp) {
        exitApp();
      } else {
        this.$router.go(-1);
      }
    },

    imgClick() {
      getImgCode({}, { loading: false }).then((data) => {
        if (data.code === 0) {
          let results = data.data;
          this.imgSrc = results.image;
          this.captchaToken = results.token;
          this.imgCode = '';
        }
      });
    },

    checkInput() {
      if (!this.fundAccount) {
        _hvueToast({
          mes: '请输入资金账号'
        });
        return false;
      }
      if (!this.idType) {
        _hvueToast({
          mes: '请选择证件类别'
        });
        return false;
      }
      if (!this.idNo) {
        _hvueToast({
          mes: '请输入证件号码'
        });
        return false;
      }
      if (!this.imgCode) {
        _hvueToast({
          mes: '请输入图形验证码'
        });
        return false;
      }
      if (this.imgCode.length !== 4) {
        _hvueToast({
          mes: '图形验证码格式不正确'
        });
        return false;
      }
      if (!this.isChecked) {
        this.$TAlert({
          title: '温馨提示',
          tips: '请确认并同意国金证券根据监管要求，收集您的身份信息用于忘记密码，我们将依法保护您的隐私安全。',
          hasCancel: true,
          confirmBtn: '确认',
          confirm: () => {
            this.isChecked = true;
            this.toNext();
          }
        })
        return false;
      }
      this.toNext();
    },

    toNext() {
      let idKind = this.idType;
      const regExp = /^\d{8}$/; // 台湾居民来往大陆通行证：8位阿拉伯数字
      if (idKind === ID_KIND.HK_MACAU_PASS && regExp.test(this.idNo)) {
        idKind = ID_KIND.TAIWAN_PASS;
      }
      $h.setSession('bizType', this.bizType);
      resetPasswordAuth({
        fundAccount: this.fundAccount,
        idNo: this.idNo,
        idKind,
        imgCode: this.imgCode,
        captcha: this.imgCode,
        captchaToken: this.captchaToken,
        opStation: this.opStation !== '' ? this.opStation : getOpStation()
      })
        .then((res) => {
          if (res.code === 0) {
            let userInfo = Object.assign({}, res.data);
            userInfo.loginFundAccount = this.fundAccount;
            $h.setSession(
              'tkTwoFactorToken',
              res.responseHeaders['tk-two-factor-token']
            );
            this.$store.commit('user/setUserInfo', userInfo);
            this.$router.push({
              name: 'phoneConfirm'
            });
            /*  let authorization = res.responseHeaders['tk-token-authorization'];
            $h.setSession('authorization', authorization);
            let userInfo = Object.assign({}, res.data);
            userInfo.loginFundAccount = this.fundAccount;
            this.$store.commit('user/setUserInfo', userInfo);
            import('@/common/flowMixinV2.js').then((a) => {
              a.initFlow.call(this, { bizType: this.bizType });
            }); */
          }
        })
        .catch((err) => {
          this.imgClick();
          this.$TAlert({ title: '请确认', tips: err });
        });
    },
    pickerCallback({ label }) {
      this.idTypeName = label;
    },
    jumpPage() {
      this.$router.push({
        name: 'fundAccRetrieve'
      });
      /* if ($hvue.platform === '0') {
        window.location.href = $hvue.customConfig.thirdPartyUrl.retrieveAccount;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.retrieveAccount,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      } */
    }
  }
};
</script>
<style scoped>
.van-popup >>> .layer_cont {
  height: auto;
}
</style>
