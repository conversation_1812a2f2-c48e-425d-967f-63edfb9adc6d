<template>
  <section class="main fixed" data-page="home">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">补开股东户</h1>
        <a class="icon_cs" v-if="isAPP" @click="accountHref"></a>
      </div>
    </header>
    <article class="content">
      <ul class="com_infolist spel">
        <li>
          <span class="tit">普通资金账户</span>
          <p v-if="loadingEd">{{ $store.state.user.userInfo.fundAccount }}</p>
        </li>
        <li>
          <span class="tit">一码通账户</span>
          <p v-if="loadingEd">{{ ymtAccount || '无' }}</p>
        </li>
      </ul>
      <ul class="market_ctlist">
        <li>
          <div class="base">
            <div class="bg"><img src="@/assets/images/bg_shanghai.png" /></div>
            <h5>上海A股</h5>
            <p
              v-if="
                stockAccountInfoSh.stockAccountList.length === 0 && loadingEd
              "
            >
              <span>未开通</span>
            </p>
            <div
              v-for="(item, i) in stockAccountInfoSh.stockAccountList"
              :key="i"
            >
              <p v-if="!item.stockAccount && item.accountStatusDesc">
                <span>{{ item.accountStatusDesc }}</span>
              </p>
              <p>
                <span v-if="item.stockAccount" class="num">{{
                  item.stockAccount
                }}</span>
                <i class="tag_zhu_span" v-show="item.mainFlag === '1'">主</i>
                <em v-if="item.accountStatusDesc" class="acct_s_tag">{{
                  item.accountStatusDesc
                }}</em>
              </p>
            </div>
            <span
              v-if="
                stockAccountInfoSh.stockAccountList.length !== 0 &&
                stockAccountInfoSh.stockAccountList[0].seatNo
              "
              class="state"
              >席位号：{{ stockAccountInfoSh.stockAccountList[0].seatNo }}</span
            >
          </div>
          <div class="opea" v-show="shButtonDiv">
            <a
              v-if="stockAccountInfoSh.buttonStyle.openAccountShow"
              class="com_btn"
              @click="
                toBizType(
                  '010044',
                  '0-3510',
                  '1',
                  stockAccountInfoSh.buttonStyle,
                  stockAccountInfoSz.buttonStyle
                )
              "
              >开通账户</a
            >
            <a
              v-if="stockAccountInfoSh.buttonStyle.appointTradeShow"
              class="com_btn"
              @click="toGJ(stockAccountInfoSh)"
              >指定交易</a
            >
            <a
              v-if="stockAccountInfoSh.buttonStyle.servicePwdShow"
              class="com_btn"
              @click="toBizSjsType('010728', '0-3508')"
              >深交所服务密码</a
            >
            <a
              v-if="stockAccountInfoSh.buttonStyle.changeAccountShow"
              class="com_btn"
              @click="
                toBizType(
                  '010729',
                  '0-3507',
                  '1',
                  stockAccountInfoSh.buttonStyle,
                  stockAccountInfoSz.buttonStyle
                )
              "
              >更换股东账户</a
            >
            <!-- <a
              v-if="stockAccountInfoSh.buttonStyle.cancelAccountShow"
              class="com_btn"
              @click="toCancel"
              >注销账户</a
            > -->
            <!-- <a
              v-if="stockAccountInfoSh.buttonStyle.serviceCallShow"
              class="com_btn"
              @click="toOther"
              >联系客服</a
            > -->
          </div>
          <div class="acct_list_tips" v-if="stockAccountInfoSh.buttonStyle.cancelAccountShow">
            <p>您的账户状态异常如需变更可联系您的服务人员或拨打客服95310咨询</p>
          </div>
        </li>
        <li>
          <div class="base">
            <div class="bg"><img src="@/assets/images/bg_shenzhen.png" /></div>
            <h5>深圳A股</h5>
            <p
              v-if="
                stockAccountInfoSz.stockAccountList.length === 0 && loadingEd
              "
            >
              <span>未开通</span>
            </p>
            <div
              v-for="(item, i) in stockAccountInfoSz.stockAccountList"
              :key="i"
            >
              <p
                style="color: #fa443a"
                v-if="!item.stockAccount && item.accountStatusDesc"
              >
                <span>{{ item.accountStatusDesc }}</span>
              </p>
              <p>
                <span v-if="item.stockAccount" class="num">{{
                  item.stockAccount
                }}</span>
                <i class="tag_zhu_span" v-show="item.mainFlag === '1'">主</i>
                <em v-if="item.accountStatusDesc" class="acct_s_tag">{{
                  item.accountStatusDesc
                }}</em>
              </p>
            </div>
            <span
              v-if="
                stockAccountInfoSz.stockAccountList.length !== 0 &&
                stockAccountInfoSz.stockAccountList[0].seatNo
              "
              class="state"
              >席位号：{{ stockAccountInfoSz.stockAccountList[0].seatNo }}</span
            >
          </div>
          <div class="opea" v-show="szButtonDiv">
            <a
              v-if="stockAccountInfoSz.buttonStyle.openAccountShow"
              class="com_btn"
              @click="
                toBizType(
                  '010044',
                  '0-3510',
                  '2',
                  stockAccountInfoSh.buttonStyle,
                  stockAccountInfoSz.buttonStyle
                )
              "
              >开通账户</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.appointTradeShow"
              class="com_btn"
              @click="toGJ(stockAccountInfoSh)"
              >指定交易</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.servicePwdShow"
              class="com_btn"
              @click="toBizSjsType('010728', '0-3508')"
              >深交所服务密码</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.changeAccountShow"
              class="com_btn"
              @click="toBGTips"
              >更换股东账户</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.cancelAccountShow"
              class="com_btn"
              @click="toCancel"
              >注销账户</a
            >
            <a
              v-if="stockAccountInfoSz.buttonStyle.serviceCallShow"
              class="com_btn"
              @click="toOther"
              >联系客服</a
            >
          </div>
        </li>
      </ul>
      <div class="open_form_box">
        <div class="com_open_form" @click="toBiztypeKHQRD">
          <span class="tit">查看开户确认单</span>
          <div class="business_date_form">
            <p class="text">交易日9:00-16:00</p>
          </div>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div class="ce_btn">
        <a
          class="p_button border"
          v-show="masterAccountShow"
          @click="toBizType('001151')"
          >设置主账</a
        >
        <a
          class="p_button"
          v-show="openShareholderAccountShow"
          @click="toBizType('010044')"
          >多开股东户</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import { bkStockAccountListqry } from '@/service/shareholdAccountService';
import { businessStrategyCheck } from '@/service/service.js';
import TxzxgUtil from '@/common/TxzxgUtil';
import { exitApp, getInstantToken, jumpThirdPartyUrl } from '@/common/util';

export default {
  data() {
    return {
      loadingEd: false,
      ymtAccount: '',
      openShareholderAccountShow: '', //新增多开股东户按钮
      masterAccountShow: '', //新增设置主帐按钮
      stockAccountInfoSh: {
        buttonStyle: {
          appointTradeShow: false,
          changeAccountShow: false,
          openAccountShow: false,
          serviceCallShow: false,
          servicePwdShow: false,
          cancelAccountShow: false
        },
        stockAccountList: []
      },
      stockAccountInfoSz: {
        buttonStyle: {
          appointTradeShow: false,
          changeAccountShow: false,
          openAccountShow: false,
          serviceCallShow: false,
          servicePwdShow: false,
          cancelAccountShow: false
        },
        stockAccountList: []
      }
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.clientId;
    },
    isAPP() {
      return $hvue.platform !== '0';
    },
    szButtonDiv() {
      return (
        this.stockAccountInfoSz.buttonStyle.openAccountShow ||
        this.stockAccountInfoSz.buttonStyle.appointTradeShow ||
        this.stockAccountInfoSz.buttonStyle.servicePwdShow ||
        this.stockAccountInfoSz.buttonStyle.changeAccountShow ||
        this.stockAccountInfoSz.buttonStyle.cancelAccountShow ||
        this.stockAccountInfoSz.buttonStyle.serviceCallShow
      );
    },
    shButtonDiv() {
      return (
        this.stockAccountInfoSh.buttonStyle.openAccountShow ||
        this.stockAccountInfoSh.buttonStyle.appointTradeShow ||
        this.stockAccountInfoSh.buttonStyle.servicePwdShow ||
        this.stockAccountInfoSh.buttonStyle.changeAccountShow
      );
    }
  },
  watch: {
    ssoLoginFlag: {
      handler(clientId) {
        console.log(clientId);
        if (clientId) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    $h.setSession('showRejectResult', null);
    $h.setSession('addShareHoldAccount', true);
    window.viewShowCallBack = this.viewShowCallBack;
  },
  mounted() {
    // this.renderingView();
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  methods: {
    accountHref() {
      getInstantToken()
        .then(({ instantToken, opStation, appId }) => {
          jumpThirdPartyUrl({
            url: `${window.$hvue.customConfig.targetUrl}/yjbwebonlineservice/onlineservice/web/onlineservice/html/index.html?instant_token=${instantToken}&op_station=${opStation}&app_id=${appId}`
          });
        })
        .catch((err) => {
          _hvueToast({ mes: err });
        });
    },

    viewShowCallBack() {
      this.renderingView();
    },

    back() {
      const txzxgUtil = new TxzxgUtil();
      $h.clearSession('addShareHoldAccount');
      if (txzxgUtil.checkTxzxg) {
        txzxgUtil.closePage();
        return;
      }
      if (!this.isAPP) {
        this.$router.back();
      } else {
        if ($h.getSession('history_list').index === 0) {
          exitApp();
        } else {
          this.$router.back();
        }
      }
    },

    renderingView() {
      businessStrategyCheck({
        strategyNo: 'wt_bggdh_init_no_parameter_check'
      }).then((res) => {
        if (res.data.strategyResult === '1') {
          bkStockAccountListqry().then((res) => {
            this.ymtAccount = res.data.acodeAccount;
            this.openShareholderAccountShow =
              res.data.openShareholderAccountShow;
            this.masterAccountShow = res.data.masterAccountShow;
            if (res.data.stockAccountInfoSh) {
              const { buttonStyle = {}, stockAccountList = [] } =
                res.data.stockAccountInfoSh;
              this.stockAccountInfoSh = { buttonStyle, stockAccountList };
            }
            if (res.data.stockAccountInfoSz) {
              const { buttonStyle = {}, stockAccountList = [] } =
                res.data.stockAccountInfoSz;
              this.stockAccountInfoSz = { buttonStyle, stockAccountList };
            }
            this.loadingEd = true;
          });
        } else {
          let tip = JSON.parse(res.data.result[0].ruleResultDesc);
          this.$TAlert({
            title: tip.title,
            tips: tip.tips,
            confirm: () => {
              this.back();
            }
          });
          return;
        }
      });
    },

    toBizSjsType(bizType, flowNo) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, flowNo, initJumpMode: '0' });
      });
    },

    toBizType(
      bizType = '',
      flowNo = '',
      exchangeType = '',
      shbuttonStyle = '',
      szbuttonStyle = ''
    ) {
      import('@/common/flowMixinV2.js').then((a) => {
        let extInitParams = {
          isChangeAccountShowSz: false,
          isChangeAccountShowSh: false,
          isOpenAccountShowSz: true,
          isOpenAccountShowSh: true
        };
        if (exchangeType !== '') {
          extInitParams = {
            exchangeType,
            isChangeAccountShowSz: szbuttonStyle.changeAccountShow,
            isChangeAccountShowSh: shbuttonStyle.changeAccountShow,
            isOpenAccountShowSz: szbuttonStyle.openAccountShow,
            isOpenAccountShowSh: shbuttonStyle.openAccountShow
          };
        }
        a.initFlow.call(this, {
          bizType,
          flowNo,
          contextParam: JSON.stringify({
            extInitParams: JSON.stringify(extInitParams)
          }),
          initJumpMode: '0'
        });
      });
    },

    toBGTips() {
      // 深市变更提示页
      this.$router.push({ name: 'changeShareAccountTips' });
    },

    toGJ(stockAccountInfoSh) {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010053', initJumpMode: '0' });
      });
      return;
    },

    toCancel() {
      // 注销账户
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010731', initJumpMode: '0' });
      });
      return;
    },

    toOther() {
      // 联系客服
      // this.$TAlert({
      //   title:'温馨提示',
      //   tips:''
      // })
      let targetUrl =
        $hvue.customConfig.targetUrl +
        '/yjbwebmoc/moc/web/moc-pro/build/goGroupView.html?groupName=onlineServiceForWeb';
      if (!this.isAPP) {
        window.location.href = targetUrl;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          // targetModule: 'open',
          params: {
            url: targetUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    toBiztypeKHQRD() {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType: '010003', initJumpMode: '0' });
      });
    }
  }
};
</script>
<style lang="less" scoped>
.com_btn {
  border-radius: 0.18rem;
  border: 1px solid #ff2840;
  background: var(--primary-b-500, #fff);
  color: var(--typography-fff, #ff2840);
}
.acct_list_tips {
  position: relative;
  bottom: 0.2rem;
  padding: 0.1rem 0.16rem 0rem;
  p {
    color: var(--tColorLightgray, #87878d);
  }
}
div.open_form_box {
  position: relative;
}
div.com_open_form {
  background: #ffffff;
  border-radius: 0.06rem;
  margin: 0.16rem;
  height: 0.56rem;
  padding: 0.16rem;
}
div.com_open_form::after {
  content: '\e619';
  font-family: 'wt-iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #bbbbbb;
  width: 0.16rem;
  height: 0.16rem;
  font-size: 0.16rem;
  line-height: 0.16rem;
  position: absolute;
  top: 0.19rem;
  right: 0.3rem;
}
div.com_open_form > .tit {
  font-size: 0.16rem;
  position: relative;
}
.business_date_form {
  position: absolute;
  top: 0.17rem;
  right: 0.5rem;
}
.business_date_form > .text {
  color: #87878d;
  font-size: 0.14rem;
}
</style>
