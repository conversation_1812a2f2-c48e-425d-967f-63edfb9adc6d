<template>
  <section class="main fixed white_bg">
    <header class="header">
      <div class="header_inner">
        <a class="icon_back" @click="back"></a>
        <h1 class="title">预约期权开户</h1>
      </div>
    </header>
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ok"></div>
          <h5>预约成功</h5>
          <p>
            温馨提示：您近期已完成了预约，请您携带本人的身份证件，于{{ preDate }}{{ preTime }}前往{{
              branchName
            }}办理，逾期需重新预约，5个交易日内不可重复预约。
          </p>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">预约期权开户</span>
              <p>预约成功</p>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <a class="p_button" @click="goAddManagerPage">添加专属服务经理</a>
        <a class="p_button border" @click="back">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  queryShOptionAppointmentInfo,
  getSysBranchInfo
} from '@/service/service';
import { openNewPage, getInstantToken, exitApp } from '@/common/util.js';

export default {
  data() {
    return {
      preDate: '',
      preTime: '',
      branchNo: '',
      branchName: ''
    };
  },
  watch: {
    showResult: {
      handler(val) {
        this.showPage = val;
      },
      immediate: true
    }
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      try {
        const { data = {} } = await queryShOptionAppointmentInfo({});
        this.preDate = data.preDate;
        this.preTime = data.preTime;
        this.branchNo = data.branchNo;

        const branchRes = await getSysBranchInfo({});
        this.branchName =
          branchRes.data.find((item) => item.branchNo === this.branchNo)
            ?.branchName || '';
      } catch (err) {
        this.$TAlert({
          title: '温馨提示',
          tips: err
        });
      }
    },
    back() {
      const isWebPlatform = $hvue.platform === '0';
      const isFirstPage = $h.getSession('history_list')?.index === 0;

      if (isWebPlatform || !isFirstPage) {
        this.$router.back();
      } else {
        exitApp();
      }
    },
    async goAddManagerPage() {
      const baseUrl = `${serviceOptions.target}/ims/guide-to-add-qywechat/index?source=102&scene=205_000009`;
      let pageUrl = baseUrl;

      if ($hvue.platform === '0') {
        try {
          const { instantToken } = await getInstantToken();
          pageUrl = `${baseUrl}&instant_token=${instantToken}`;
        } catch (error) {
          console.error('获取instantToken失败:', error);
        }
      }
      openNewPage({
        pageUrl,
        title: '添加企业微信'
      });
    }
  }
};
</script>

<style scoped>
/* 组件样式可以根据需要添加 */
</style>
