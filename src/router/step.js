export default [
  {
    path: '/step/test',
    name: 'test',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/test.vue'),
    meta: {
      title: '测试节点'
    }
  },
  {
    path: '/step/baseCheck',
    name: 'baseCheck',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/baseCheck/baseCheck.vue'
      ),
    meta: {
      title: '前置条件检查'
    }
  },
  {
    path: '/step/enterCheck',
    name: 'enterCheck',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/enterCheck/enterCheck.vue'
      ),
    meta: {
      title: '准入条件检查'
    }
  },
  {
    path: '/step/baseCheckBase',
    name: 'baseCheckBase',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/baseCheck/baseCheckBase.vue'
      ),
    meta: {
      title: '前置条件检查'
    }
  },
  {
    path: '/step/enterCheckBase',
    name: 'enterCheckBase',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/enterCheck/enterCheckBase.vue'
      ),
    meta: {
      title: '准入条件检查'
    }
  },
  {
    path: '/step/orgBaseCheck',
    name: 'orgBaseCheck',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/baseCheck/orgBaseCheck.vue'
      ),
    meta: {
      title: '前置条件检查'
    }
  },
  {
    path: '/step/orgBaseInfo',
    name: 'orgBaseInfo',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/orgBaseInfo.vue'),
    meta: {
      title: '商照信息确认'
    }
  },
  {
    path: '/step/orgCardInit',
    name: 'orgCardInit',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/orgCardInit.vue'),
    meta: {
      title: '客户证件上传'
    }
  },
  {
    path: '/step/faceRecognition',
    name: 'faceRecognition',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/faceRecognition.vue'),
    meta: {
      title: '活体认证'
    }
  },
  {
    path: '/step/insuranceInfo',
    name: 'insuranceInfo',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/insuranceInfo.vue'),
    meta: {
      title: '追保信息填写'
    }
  },
  {
    path: '/step/holderEdu',
    name: 'holderEdu',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/holderEdu.vue'),
    meta: {
      title: '投资者教育'
    }
  },
  {
    path: '/step/knowledgeSurvey',
    name: 'knowledgeSurvey',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/knowledgeSurvey/knowledgeSurvey.vue'
      ),
    meta: {
      title: '知识测评'
    }
  },
  {
    path: '/step/creditSurvey',
    name: 'creditSurvey',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/creditSurvey.vue'),
    meta: {
      title: '征信测评'
    }
  },
  // {
  //   path: '/step/knowledge',
  //   name: 'knowledge',
  //   component: () => import('@/views/step/knowledgeSurvey/knowledge.vue'),
  //   meta: {
  //     title: '知识测评'
  //   }
  // },
  //   {
  //     path: '/step/suitability',
  //     name: 'suitability',
  //     component: () => import('@/views/step/suitability.vue'),
  //     meta: {
  //       title: '适当性匹配'
  //     }
  //   },
  {
    path: '/step/selectAccount',
    name: 'selectAccount',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/selectAccount.vue'),
    meta: {
      title: '选择账户'
    }
  },
  {
    path: '/step/signAgree',
    name: 'signAgree',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/signAgree/signAgree.vue'
      ),
    meta: {
      title: '协议签署'
    }
  },
  {
    path: '/step/signContract',
    name: 'signContract',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/signAgree/signContract.vue'
      ),
    meta: {
      title: '协议签署'
    }
  },
  {
    path: '/step/idApprove',
    name: 'idApprove',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/idApprove.vue'),
    meta: {
      title: '上传身份证并确认'
    }
  },
  {
    path: '/step/selectAccountImport',
    name: 'selectAccountImport',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/selectAccountImport.vue'
      ),
    meta: {
      title: '确认账户并上传材料'
    }
  },
  {
    path: '/step/videoApprove',
    name: 'videoApprove',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/videoApprove.vue'),
    meta: {
      title: '临柜双录见证'
    }
  },
  {
    path: '/step/witnessTwo',
    name: 'witnessTwo',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/witnessTwo.vue'),
    meta: {
      title: '临柜双录见证'
    }
  },
  {
    path: '/step/specialInformation',
    name: 'specialInformation',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/specialInformation.vue'
      ),
    meta: {
      title: '特殊信息申报'
    }
  },
  {
    path: '/step/materialUpload',
    name: 'materialUpload',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/materialUpload.vue'),
    meta: {
      title: '资料上传'
    }
  },
  {
    path: '/step/accountResult',
    name: 'accountResult',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/result/accountResult.vue'
      ),
    meta: {
      title: '结果页'
    }
  },
  {
    path: '/step/result',
    name: 'result',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/result/result.vue'),
    meta: {
      title: '结果页'
    }
  },
  {
    path: '/step/introduceBusiness',
    name: 'introduceBusiness',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/introduceBusiness.vue'
      ),
    meta: {
      title: '业务介绍'
    }
  },
  {
    path: '/step/uploadIdCardInit',
    name: 'uploadIdCardInit',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/uploadIdCardInit.vue'
      ),
    meta: {
      title: '上传身份证'
    }
  },
  {
    path: '/step/bankInfo',
    name: 'bankInfo',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/bankInfo.vue'),
    meta: {
      title: '三方存管'
    }
  },
  {
    path: '/step/getCreditApplyQry',
    name: 'getCreditApplyQry',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/getCreditApplyQry.vue'
      ),
    meta: {
      title: '评级授信'
    }
  },
  {
    path: '/step/baseInfo',
    name: 'baseInfo',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/baseInfo/wxChangeBaseInfo.vue'
      ),
    meta: {
      title: '客户信息确认'
    }
  },
  {
    path: '/step/idSupply',
    name: 'idSupply',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/idSupply.vue'),
    meta: {
      title: '客户信息确认'
    }
  },
  {
    path: '/step/appointmentInfo',
    name: 'appointmentInfo',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/appointmentInfo.vue'),
    meta: {
      title: '预约信息填写'
    }
  },
  {
    path: '/step/appointmentResult',
    name: 'appointmentResult',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/appointmentResult.vue'
      ),
    meta: {
      title: '提交申请'
    }
  },
  {
    path: '/step/setMarketAndPwd',
    name: 'setMarketAndPwd',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/setMarketAndPwd.vue'),
    meta: {
      title: '市场及密码设置'
    }
  },
  {
    path: '/step/suitability',
    name: 'suitabilityBase',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/suitabilityBase.vue'),
    meta: {
      title: '适当性匹配'
    }
  },
  {
    path: '/step/witnessType',
    name: 'witnessType',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/witnessType.vue'),
    meta: {
      title: '选择见证方式'
    }
  },
  {
    path: '/step/groupPhoto',
    name: 'groupPhoto',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/groupPhoto.vue'),
    meta: {
      title: '见证人合照'
    }
  },
  {
    path: '/step/permissionSelect',
    name: 'witnessType',
    component: () =>
      import(
        /* webpackChunkName: "step" */ '@/views/step/permissionSelect.vue'
      ),
    meta: {
      title: '信用权限预选择'
    }
  },
  {
    path: '/step/setSignPwd',
    name: 'setSignPwd',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/setSignPwd.vue'),
    meta: {
      title: '协议签署密码设置'
    }
  },
  {
    path: '/step/wxResult',
    name: 'wxResult',
    component: () =>
      import(/* webpackChunkName: "step" */ '@/views/step/result/wxResult.vue'),
    meta: {
      title: '办理结果'
    }
  }
];
