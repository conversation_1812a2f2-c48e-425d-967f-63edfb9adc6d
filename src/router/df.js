export default [
  {
    path: '/lr/chooseAppointmentOrTeach',
    name: 'chooseAppointmentOrTeach',
    component: () =>
      import(
        /* webpackChunkName: "df" */ '@/views/df/chooseAppointmentOrTeach.vue'
      ),
    meta: {
      title: '预约页面'
    }
  },
  {
    path: '/lr/faceRecognition',
    name: 'faceRecognition',
    component: () =>
      import(/* webpackChunkName: "df" */ '@/views/df/faceRecognition.vue'),
    meta: {
      title: '人脸识别'
    }
  },
  {
    path: '/lr/chooseInputOrTeach',
    name: 'chooseInputOrTeach',
    component: () =>
      import(/* webpackChunkName: "df" */ '@/views/df/chooseInputOrTeach.vue'),
    meta: {
      title: '观看投教视频'
    }
  },
  {
    path: '/lr/selectBusinessDepartment',
    name: 'selectBusinessDepartment',
    component: () =>
      import(
        /* webpackChunkName: "df" */ '@/views/df/selectBusinessDepartment.vue'
      ),
    meta: {
      title: '选择预约时间'
    }
  },
  {
    path: '/lr/changeBusinessDepartment',
    name: 'changeBusinessDepartment',
    component: () =>
      import(
        /* webpackChunkName: "df" */ '@/views/df/changeBusinessDepartment.vue'
      ),
    meta: {
      title: '修改预约信息'
    }
  },
  {
    path: '/lr/preResult',
    name: 'preResult',
    component: () =>
      import(/* webpackChunkName: "df" */ '@/views/df/preResult.vue'),
    meta: {
      title: '预约结果'
    }
  }
];
