export default [
  {
    path: '/form/container/*',
    name: 'form/container',
    component: () =>
      import(/* webpackChunkName: "form" */ '@/views/form/container.vue'),
    meta: {
      title: '业务办理'
    }
  },
  {
    path: '/form/profContainer/*',
    name: 'form/profContainer',
    component: () =>
      import(/* webpackChunkName: "form" */ '@/views/form/profContainer.vue'),
    meta: {
      title: ''
    }
  },
  {
    path: '/form/preview',
    name: 'form/preview',
    component: () =>
      import(/* webpackChunkName: "form" */ '@/views/form/preview.vue'),
    meta: {
      title: 'formily表单预览'
    }
  },
  {
    path: '/form/subpage/*',
    name: 'form/subpage',
    component: () =>
      import(/* webpackChunkName: "form" */ '@/views/form/subpage.vue'),
    meta: {
      title: ''
    }
  }
];
