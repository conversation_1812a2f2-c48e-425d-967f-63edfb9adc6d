/**
 * 控制是否截屏录屏
 */
export function nativeFunc60094(paramMap = {}) {
  // 检查当前域名，如果是指定的测试环境域名，则不执行该方法
  if (window.location.href.includes('/uat2fzsdbusiness.yjbtest.com/')) {
    console.log('nativeFunc60094 在登录页面不执行');
    return;
  }

  console.log('nativeFunc60094');
  const {
    isInterceptScreenshot = '0',
    screenCaptureTip = '',
    screenRecordingTip = ''
  } = paramMap;
  const res = $h.callMessageNative({
    funcNo: '60094',
    moduleName: $hvue.customConfig.moduleName,
    isInterceptScreenshot, //控制是否截屏录屏做提示（Android可以禁止，iOS只能提示）　1：截屏录屏提示，其他不提示；默认不提示
    screenCaptureTip, //截屏提示语（仅仅iOS支持，安卓是系统提示不支持）
    screenRecordingTip //录屏提示语（仅仅iOS支持，安卓是系统提示不支持）
  });
  console.log('nativeFunc60094 callback=' + JSON.stringify(res));
};
