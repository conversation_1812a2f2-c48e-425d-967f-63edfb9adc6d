<template>
  <transition name="branch-selector" appear>
    <div v-if="visible" class="branch-selector-wrapper">
      <div class="dialog_overlay" @click="close"></div>
      <div class="layer_box">
        <div class="layer_tit">
          <h3 class="text_left">请选择营业部</h3>
          <a class="close" @click.prevent="close"></a>
        </div>
        <div class="search_box">
          <div class="search_input">
            <i class="icon"></i>
            <input
              class="t1"
              type="text"
              placeholder="输入营业部名称/位置"
              v-model="searchKeyword"
            />
          </div>
        </div>

        <!-- 面包屑导航 -->
        <div class="sele_lyinfo" v-show="!searchKeyword.trim()">
          <span
            v-for="(breadcrumb, index) in breadcrumbs"
            :key="index"
            :class="breadcrumb.class"
            @click="breadcrumb.handler"
            v-show="breadcrumb.visible"
          >
            {{ breadcrumb.text }}
          </span>
        </div>

        <div class="layer_cont">
          <div class="select_module">
            <!-- 动态标题 -->
            <h5 class="title" v-show="currentStepConfig.showTitle">
              {{ currentStepConfig.title }}
            </h5>

            <!-- 动态列表 -->
            <ul :class="currentStepConfig.listClass">
              <li
                v-for="item in currentStepConfig.data"
                :key="currentStepConfig.getItemText(item)"
                :class="{
                  active:
                    currentStep === STEPS.BRANCH &&
                    branchData.branchId === item.branchId
                }"
                @click="currentStepConfig.handler(item)"
              >
                <!-- 营业部视图使用h5标签 -->
                <template v-if="isBranchView">
                  <h5>{{ currentStepConfig.getItemText(item) }}</h5>
                  <p>{{ item.address }}</p>
                  <p>{{ item.tel }}</p>
                </template>
                <!-- 省份和城市视图使用span标签 -->
                <template v-else>
                  <span :class="currentStepConfig.getItemClass(item)">
                    {{ currentStepConfig.getItemText(item) }}
                  </span>
                </template>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
// 选择步骤常量
const STEPS = {
  PROVINCE: 'province',
  CITY: 'city',
  BRANCH: 'branch'
};

export default {
  name: 'BranchSelector',
  data() {
    return {
      STEPS, // 暴露给模板使用
      branchData: {
        provinceId: '',
        provinceName: '',
        cityId: '',
        cityName: '',
        branchId: '',
        branchNo: '',
        branchName: '',
        address: '',
        tel: ''
      },
      forceStep: null, // 强制显示的步骤，用于切换视图而不清空数据
      searchKeyword: '' // 搜索关键词
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    branchList: {
      type: Array,
      default: () => []
    }
  },
  watch: {},
  computed: {
    // 当前选择步骤
    currentStep() {
      // 如果有强制步骤，优先使用
      if (this.forceStep) return this.forceStep;

      if (!this.branchData.provinceName) return STEPS.PROVINCE;
      if (!this.branchData.cityName) return STEPS.CITY;
      return STEPS.BRANCH;
    },

    // 是否为营业部视图
    isBranchView() {
      return this.currentStep === STEPS.BRANCH || this.searchKeyword.trim();
    },

    // 处理后的营业部数据
    processBranchListData() {
      console.info('营业部信息', this.branchList);
      if (!this.branchList || this.branchList.length === 0) return [];
      const groupedData = this.groupBranchesByProvinceAndCity(this.branchList);
      console.info('分类后的营业部数据', groupedData);
      return groupedData;
    },

    // 城市列表
    cityList() {
      if (!this.branchData.provinceName) return [];
      const province = this.processBranchListData.find(
        (item) => item.provinceName === this.branchData.provinceName
      );
      return province ? province.cities : [];
    },

    // 营业部列表
    areaBranchList() {
      if (!this.branchData.cityName) return [];
      const city = this.cityList.find(
        (item) => item.cityName === this.branchData.cityName
      );
      const branches = city ? city.branches : [];

      return this.searchKeyword.trim()
        ? this.filterBranches(branches)
        : branches;
    },

    // 全局营业部搜索列表
    globalSearchBranchList() {
      if (!this.searchKeyword.trim()) return [];

      const allBranches = [];
      this.processBranchListData.forEach((province) => {
        province.cities.forEach((city) => {
          const filteredBranches = this.filterBranches(city.branches);
          filteredBranches.forEach((branch) => {
            allBranches.push({
              ...branch,
              provinceName: province.provinceName,
              cityName: city.cityName
            });
          });
        });
      });

      return allBranches;
    },

    // 面包屑导航配置
    breadcrumbs() {
      const createBreadcrumb = (name, handler, visible = true) => ({
        text: name || '请选择',
        class: { active: !name, off: !!name },
        handler,
        visible
      });

      return [
        createBreadcrumb(this.branchData.provinceName, this.goToProvinceStep),
        createBreadcrumb(
          this.branchData.cityName,
          this.goToCityStep,
          !!this.branchData.provinceName
        ),
        createBreadcrumb(
          this.branchData.branchName,
          this.goToBranchStep,
          !!this.branchData.cityName
        )
      ];
    },

    // 当前步骤配置
    currentStepConfig() {
      // 如果有搜索关键词，显示全局搜索结果
      if (this.searchKeyword.trim()) {
        return {
          listClass: 'network_list',
          data: this.globalSearchBranchList,
          handler: this.selectBranchFromSearch,
          getItemText: (item) => item.branchName,
          getItemClass: (item) => ({
            local: this.branchData.branchId === item.branchId
          })
        };
      }

      const configs = {
        [STEPS.PROVINCE]: {
          title: '省份地区',
          showTitle: true,
          listClass: 'select_list_2',
          data: this.processBranchListData,
          handler: this.selectProvince,
          getItemText: (item) => item.provinceName,
          getItemClass: (item) => ({
            local: this.branchData.provinceName === item.provinceName
          })
        },
        [STEPS.CITY]: {
          title: '',
          showTitle: false,
          listClass: 'select_list_2',
          data: this.cityList,
          handler: this.selectCity,
          getItemText: (item) => item.cityName,
          getItemClass: (item) => ({
            local: this.branchData.cityName === item.cityName
          })
        },
        [STEPS.BRANCH]: {
          title: '',
          showTitle: false,
          listClass: 'network_list',
          data: this.areaBranchList,
          handler: this.selectBranch,
          getItemText: (item) => item.branchName,
          getItemClass: (item) => ({
            local: this.branchData.branchId === item.branchId
          })
        }
      };
      return configs[this.currentStep] || configs[STEPS.PROVINCE];
    }
  },
  methods: {
    close() {
      this.$emit('update:visible', false);
    },

    // 按省份和城市分组营业部数据
    groupBranchesByProvinceAndCity(branchList) {
      const result = {};
      branchList.forEach((branch) => {
        const { provinceId, provinceName, cityId, cityName } = branch;
        // 初始化省份
        if (!result[provinceId]) {
          result[provinceId] = {
            provinceId,
            provinceName,
            cities: {}
          };
        }
        // 初始化城市
        if (!result[provinceId].cities[cityId]) {
          result[provinceId].cities[cityId] = {
            cityId,
            cityName,
            branches: []
          };
        }
        // 添加营业部到对应城市
        result[provinceId].cities[cityId].branches.push(branch);
      });
      // 转换为数组格式，便于模板渲染
      return Object.values(result).map((province) => ({
        ...province,
        cities: Object.values(province.cities)
      }));
    },
    // 统一的字段重置方法
    resetBranchData(level = 'all') {
      const resetMap = {
        all: [
          'provinceId',
          'provinceName',
          'cityId',
          'cityName',
          'branchId',
          'branchNo',
          'branchName',
          'address',
          'tel'
        ],
        city: [
          'cityId',
          'cityName',
          'branchId',
          'branchNo',
          'branchName',
          'address',
          'tel'
        ],
        branch: ['branchId', 'branchNo', 'branchName', 'address', 'tel']
      };

      const fieldsToReset = resetMap[level] || resetMap.all;
      fieldsToReset.forEach((field) => {
        this.branchData[field] = '';
      });

      // 清空搜索关键词
      this.searchKeyword = '';
    },

    // 营业部搜索过滤方法
    filterBranches(branches) {
      const keyword = this.searchKeyword.trim().toLowerCase();
      return branches.filter(
        (branch) =>
          branch.branchName.toLowerCase().includes(keyword) ||
          (branch.address && branch.address.toLowerCase().includes(keyword))
      );
    },

    // 设置选择数据的通用方法
    setBranchData(data) {
      Object.assign(this.branchData, data);
    },

    // 从搜索结果选择营业部
    selectBranchFromSearch(item) {
      // 设置完整的省市区信息
      this.branchData.provinceName = item.provinceName;
      this.branchData.cityName = item.cityName;
      this.branchData.branchName = item.branchName;
      this.branchData.branchId = item.branchId;
      this.branchData.branchNo = item.branchNo;
      this.branchData.address = item.address || '';
      this.branchData.tel = item.tel || '';

      // 清除强制步骤和搜索关键词
      this.forceStep = null;
      this.searchKeyword = '';

      // 触发选择事件
      this.$emit('select', this.branchData);
      this.close();
    },

    selectProvince({ provinceName, provinceId }) {
      this.setBranchData({ provinceName, provinceId });
      this.resetBranchData('city');
      this.forceStep = null;
    },

    selectCity({ cityName, cityId }) {
      this.setBranchData({ cityName, cityId });
      this.resetBranchData('branch');
      this.forceStep = null;
    },

    selectBranch({ branchName, branchId, branchNo, address, tel }) {
      this.setBranchData({
        branchName,
        branchId,
        branchNo,
        address: address || '',
        tel: tel || ''
      });
      this.forceStep = null;
      this.$emit('select', this.branchData);
      this.close();
    },

    // 切换到省份选择步骤（不清空数据）
    goToProvinceStep() {
      this.forceStep = STEPS.PROVINCE;
    },

    // 切换到城市选择步骤（不清空数据）
    goToCityStep() {
      this.forceStep = STEPS.CITY;
    },

    // 切换到营业部选择步骤（不清空数据）
    goToBranchStep() {
      this.forceStep = STEPS.BRANCH;
    }
  }
};
</script>

<style scoped>
/* 过渡动画样式 */
.branch-selector-enter-active,
.branch-selector-leave-active {
  transition: all 0.3s ease;
}

.branch-selector-enter-from,
.branch-selector-leave-to {
  opacity: 0;
}

.branch-selector-enter-from .layer_box {
  transform: translateY(100%);
}

.branch-selector-leave-to .layer_box {
  transform: translateY(100%);
}

.branch-selector-enter-to .layer_box,
.branch-selector-leave-from .layer_box {
  transform: translateY(0);
}

/* 弹窗容器样式 */
.branch-selector-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

/* 弹窗主体过渡效果 */
.layer_box {
  transition: transform 0.3s ease;
  transform-origin: center center;
}

/* 遮罩层过渡效果 */
.dialog_overlay {
  transition: opacity 0.3s ease;
}
</style>
