<template>
  <van-popup v-model="showPicker" position="bottom" round>
    <div class="appointment-picker">
      <div class="picker-header">
        <button class="cancel-btn" @click="cancel">取消</button>
        <div class="title">预约日期</div>
        <button class="confirm-btn" @click="confirm">确定</button>
      </div>

      <div class="picker-content" v-if="!loading">
        <div class="selection-container">
          <!-- 日期选择区域 -->
          <div class="date-section">
            <div class="date-list" v-if="appointDateList.length > 0">
              <div
                v-for="(date, index) in appointDateList"
                :key="index"
                class="date-item"
                :class="{
                  active: selectedDateIndex === index,
                  disabled:
                    selectedDateIndex !== -1 && selectedDateIndex !== index
                }"
                @click="selectDate(index)"
              >
                <div class="date-text">{{ date }}</div>
              </div>
            </div>
            <div class="empty-hint" v-else>
              <div class="empty-text">暂无可预约日期</div>
            </div>
          </div>

          <!-- 时间段选择区域 -->
          <div class="time-section" v-if="selectedDateIndex !== -1">
            <div class="time-list" v-if="appointTimeList.length > 0">
              <div
                v-for="(time, index) in appointTimeList"
                :key="index"
                class="time-item"
                :class="{ active: selectedTimeIndex === index }"
                @click="selectTime(index)"
              >
                <div class="time-text">{{ time }}</div>
              </div>
            </div>
            <div class="empty-hint" v-else>
              <div class="empty-text">暂无可预约时间段</div>
            </div>
          </div>
        </div>
      </div>

      <div class="loading-state" v-if="loading">
        <van-loading size="24px">加载中...</van-loading>
      </div>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'AppointmentDatePicker',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => ({}),
      validator: (value) => {
        return typeof value === 'object' && value !== null;
      }
    },
    appointDateList: {
      type: Array,
      default: () => []
    },
    appointTimeList: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedDateIndex: -1,
      selectedTimeIndex: -1
    };
  },

  computed: {
    showPicker: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    hasSelectedDate() {
      return this.selectedDateIndex !== -1;
    },
    hasSelectedTime() {
      return this.selectedTimeIndex !== -1;
    },
    selectedDate() {
      return this.hasSelectedDate && this.appointDateList.length > 0
        ? this.appointDateList[this.selectedDateIndex]
        : null;
    },
    selectedTime() {
      return this.hasSelectedTime && this.appointTimeList.length > 0
        ? this.appointTimeList[this.selectedTimeIndex]
        : null;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.syncSelectionWithValue();
      }
    },
    appointDateList: {
      handler() {
        this.syncSelectionWithValue();
      },
      immediate: true
    },
    appointTimeList: {
      handler() {
        this.syncSelectionWithValue();
      },
      immediate: true
    },
    value: {
      handler() {
        this.syncSelectionWithValue();
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 同步选择状态与外部传入的值
    syncSelectionWithValue() {
      if (!this.value || typeof this.value !== 'object') return;

      // 同步日期选择
      if (this.value.date && this.appointDateList.length > 0) {
        const dateIndex = this.appointDateList.findIndex(date => date === this.value.date);
        if (dateIndex !== -1) {
          this.selectedDateIndex = dateIndex;
        }
      }

      // 同步时间选择
      if (this.value.time && this.appointTimeList.length > 0) {
        const timeIndex = this.appointTimeList.findIndex(time => time === this.value.time);
        if (timeIndex !== -1) {
          this.selectedTimeIndex = timeIndex;
        }
      }
    },

    selectDate(index) {
      if (index >= 0 && index < this.appointDateList.length) {
        this.selectedDateIndex = index;
        this.selectedTimeIndex = -1; // 重置时间选择
      }
    },
    selectTime(index) {
      if (index >= 0 && index < this.appointTimeList.length) {
        this.selectedTimeIndex = index;
      }
    },

    cancel() {
      this.selectedDateIndex = -1;
      this.selectedTimeIndex = -1;
      this.showPicker = false;
      this.$emit('cancel');
    },
    confirm() {
      // 验证选择
      if (!this.hasSelectedDate) {
        this.showError('请选择预约日期');
        return;
      }
      if (!this.hasSelectedTime) {
        this.showError('请选择预约时间段');
        return;
      }

      // 构建结果
      const result = {
        date: this.selectedDate,
        time: this.selectedTime,
        label: `${this.selectedDate} ${this.selectedTime}`,
        dateIndex: this.selectedDateIndex,
        timeIndex: this.selectedTimeIndex
      };

      this.showPicker = false;
      this.$emit('confirm', result);
    },
    showError(message) {
      if (typeof _hvueToast === 'function') {
        _hvueToast({ mes: message });
      } else {
        console.warn('Toast function not available:', message);
      }
    }
  }
};
</script>

<style scoped>
.appointment-picker {
  background: white;
  border-radius: 16px 16px 0 0;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #ebedf0;
  flex-shrink: 0;
}

.cancel-btn,
.confirm-btn {
  background: none;
  border: none;
  font-size: 16px;
  padding: 0;
  cursor: pointer;
  min-width: 40px;
}

.cancel-btn {
  color: #969799;
  text-align: left;
}

.confirm-btn {
  color: #f93838;
  font-weight: 500;
  text-align: right;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  text-align: center;
  flex: 1;
}

.picker-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.selection-container {
  display: flex;
  gap: 20px;
  min-height: 300px;
}

.date-section {
  flex: 1;
  min-width: 0;
}

.time-section {
  flex: 1;
  min-width: 0;
}

.date-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-item {
  padding: 16px 12px;
  border: none;
  border-radius: 0;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  background: transparent;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.date-item:last-child {
  border-bottom: none;
}

.date-item.active {
  background-color: #f93838;
  color: white;
  border-radius: 6px;
  border-bottom: 1px solid #f93838;
}

.date-item.disabled {
  color: #c8c9cc;
  opacity: 0.5;
}

.time-item {
  padding: 16px 12px;
  border: none;
  border-radius: 0;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  background: transparent;
  border-bottom: 1px solid #f0f0f0;
}

.time-item:last-child {
  border-bottom: none;
}

.time-item.active {
  background-color: #f93838;
  color: white;
  border-radius: 6px;
  border-bottom: 1px solid #f93838;
}

.date-text {
  font-size: 16px;
  font-weight: 400;
  color: inherit;
}

.time-text {
  font-size: 16px;
  font-weight: 400;
  color: inherit;
}

.empty-hint {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.empty-text {
  font-size: 14px;
  color: #969799;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
}
</style>
