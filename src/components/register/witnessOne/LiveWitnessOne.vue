<template>
  <fragment>
    <section
      v-if="!loading && !startH5"
      class="main fixed white_bg"
      data-page="home"
      style="position: fixed"
    >
      <t-header @back="back"></t-header>
      <article class="content">
        <template v-if="pageStep === 0">
          <div class="fc_basebox">
            <h5>请进行人脸识别来认证身份</h5>
            <p>为了确保本人操作我们将进行人脸识别</p>
            <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
          </div>
          <div class="lz_tipbox">
            <h5 class="title">视频环节请注意以下事项：</h5>
            <ul>
              <li>
                <i><img src="@/assets/images/p_fc_tp01.png" /></i
                ><span>确保光线清晰</span>
              </li>
              <li>
                <i><img src="@/assets/images/p_fc_tp04.png" /></i
                ><span>远离嘈杂环境</span>
              </li>
              <li>
                <i><img src="@/assets/images/p_fc_tp03.png" /></i
                ><span>不能带帽子</span>
              </li>
            </ul>
          </div>
        </template>
        <template v-else-if="pageStep === 1">
          <div class="video_compage">
            <div class="review_video">
              <div class="pic"><img src="" /></div>
              <div class="vd_result_box">
                <span class="ok">人脸识别通过</span>
              </div>
            </div>
            <div class="video_info">
              <h5>系统将留存此照片，请确认后提交!</h5>
              <ul>
                <li><i></i>须为正面免冠照片</li>
                <li><i></i>须完整面部出镜，无反光、遮挡、五官不清晰等情形</li>
              </ul>
            </div>
          </div>
        </template>
        <template v-else-if="pageStep === 2">
          <div class="result_page">
            <div class="result_tips">
              <div class="icon vedio_error"></div>
              <h5>{{ livingErrorParam.title }}</h5>
              <p>{{ livingErrorParam.context }}</p>
            </div>
          </div>
        </template>
        <template v-else-if="pageStep === 3">
          <div class="result_page">
            <div class="result_tips">
              <div class="icon vedio_error"></div>
              <h5></h5>
              <p>{{ maxErrorMsg }}</p>
            </div>
          </div>
        </template>
        <template v-else-if="pageStep === 5">
          <div class="fc_sbbox">
            <div class="fc_imgbox ing">
              <div class="pic"><img :src="livingImageBase64" /></div>
            </div>
            <h5>识别中…</h5>
          </div>
        </template>
        <template v-else-if="pageStep === 4">
          <div class="fc_basebox">
            <h5>请录制一段5~10秒的视频</h5>
            <p>
              请以清晰话术回答视频中的问题，我们将根据您的回答确认您的意愿。
            </p>
            <div class="pic"><img src="@/assets/images/video_face2.png" /></div>
          </div>
          <div class="lz_tipbox">
            <h5 class="title">视频环节请注意以下事项：</h5>
            <ul>
              <li>
                <i><img src="@/assets/images/p_fc_tp01.png" /></i
                ><span>确保光线清晰</span>
              </li>
              <li>
                <i><img src="@/assets/images/p_fc_tp04.png" /></i
                ><span>远离嘈杂环境</span>
              </li>
              <li>
                <i><img src="@/assets/images/p_fc_tp03.png" /></i
                ><span>不能戴帽子</span>
              </li>
            </ul>
          </div>
        </template>
        <template v-else-if="pageStep === 6">
          <div class="upload_progress">
            <div class="progress_chart">
              <i class="bg">
                <svg
                  width="193"
                  height="193"
                  viewBox="0 0 193 193"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M0 96.5C0 43.2045 43.2045 0 96.5 0C149.795 0 193 43.2045 193 96.5C193 97.3284 192.328 98 191.5 98C190.672 98 190 97.3284 190 96.5C190 44.8614 148.139 3 96.5 3C44.8614 3 3 44.8614 3 96.5C3 148.139 44.8614 190 96.5 190V193C43.2045 193 0 149.795 0 96.5Z"
                  />
                </svg>
              </i>
              <div class="img"></div>
            </div>
            <h5>
              视频上传中(<span>{{ progressCount }}</span
              >%)
            </h5>
          </div>
        </template>
        <template v-else-if="pageStep === 7">
          <div class="result_page">
            <div class="result_tips">
              <div class="icon vedio_ok"></div>
              <h5>恭喜! 视频已录制完成</h5>
              <p>您可以继续下一步</p>
            </div>
          </div>
        </template>
      </article>
      <footer class="footer">
        <div class="ce_btn" v-if="[0].includes(pageStep)">
          <a class="p_button" @click="start">开始认证</a>
        </div>
        <div class="ce_btn" v-if="[3].includes(pageStep)">
          <a class="p_button" @click="failure">{{ recordNtnText }}</a>
        </div>
        <div class="ce_btn" v-if="[4].includes(pageStep)">
          <a
            class="p_button"
            :class="{ disabled: videoUploadLoading }"
            @click="record"
          >
            开始录制
          </a>
        </div>
        <div class="ce_btn" v-else-if="[2].includes(pageStep)">
          <a class="p_button border" @click="startAppLiving">重新识别</a>
        </div>
        <div class="ce_btn" v-else-if="[7].includes(pageStep)">
          <a class="p_button" @click="next">下一步</a>
        </div>
      </footer>
    </section>
    <fragment v-if="startH5">
      <iframe
        v-if="videoIframeShow"
        style="
          position: fixed;
          width: 100%;
          height: 100vh;
          border: 0;
          top: 0;
          z-index: 9999;
        "
        allow="microphone;camera;midi;encrypted-media;"
        ref="iframe"
        @load="iframeLoad"
        :src="iframeUrl"
      ></iframe>

      <section
        v-if="!videoIframeShow"
        class="main fixed white_bg"
        data-page="home"
        style="position: fixed"
      >
        <t-header @back="back"></t-header>
        <article class="content">
          <template v-if="pageStep === 4">
            <div class="fc_basebox">
              <h5>请录制一段5~10秒的视频</h5>
              <p>
                请以清晰话术回答视频中的问题，我们将根据您的回答确认您的意愿。
              </p>
              <div class="pic">
                <img src="@/assets/images/video_face2.png" />
              </div>
            </div>
            <div class="lz_tipbox">
              <h5 class="title">视频环节请注意以下事项：</h5>
              <ul>
                <li>
                  <i><img src="@/assets/images/p_fc_tp01.png" /></i
                  ><span>确保光线清晰</span>
                </li>
                <li>
                  <i><img src="@/assets/images/p_fc_tp04.png" /></i
                  ><span>远离嘈杂环境</span>
                </li>
                <li>
                  <i><img src="@/assets/images/p_fc_tp03.png" /></i
                  ><span>不能戴帽子</span>
                </li>
              </ul>
            </div>
          </template>
          <template v-else-if="pageStep === 7">
            <div class="result_page">
              <div class="result_tips">
                <div class="icon vedio_ok"></div>
                <h5>恭喜! 视频已录制完成</h5>
                <p>您可以继续下一步</p>
              </div>
            </div>
          </template>
        </article>
        <footer class="footer">
          <div class="ce_btn" v-if="[4].includes(pageStep)">
            <a
              class="p_button"
              :class="{ disabled: videoUploadLoading }"
              @click="record"
            >
              开始录制
            </a>
          </div>
          <div class="ce_btn" v-else-if="[7].includes(pageStep)">
            <a class="p_button" @click="next">下一步</a>
          </div>
        </footer>
      </section>
    </fragment>
  </fragment>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import {
  addClientCritMark,
  statusQuery,
  videoAddCertificate,
  videoGetJwtToken,
  getConfigMap,
  videoGetScriptV2,
  securityVerifyConfig,
  videoOneRegist
} from '@/service/service';
import { OrderOnTheWay } from '@/service/lrService';
import {
  faceLivingFaceCompareApp,
  submitLivingLog,
  faceLivingResult,
  getAppNativeVideoConfig
} from '@/service/videoOneService.js';
import {
  filterBase64Pre,
  uploadVideo,
  isSupportWebRTC
} from '@/common/util.js';

export default {
  name: 'LiveWitnessOne',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      pageStep: 0, //页面步骤 0活体识别准备页 1活体识别通过 2活体识别失败 3活体识别/智能录制失败到达上限 4视频录制准备页 5活体识别图片上传 6录制视频上传中 7录制上传成功
      deviceSysVersion: '',
      devicePlatform: '',
      startH5: false,
      loading: true,
      videoUploadLoading: false,

      videoIframeShow: false, // iframe显示控制
      liveFailCount: 0, //活体识别失败次数
      recordFailCount: 0, //智能录制失败次数
      liveFailCountMax: 3,
      recordFailCountMax: 3,
      livingImageBase64: '',
      progressCount: 0,
      livingErrorParam: {
        title: '',
        context: ''
      }, //活体流程失败文案
      questionArray: [], //语音播报问题对象json数组
      livingLogParam: undefined,
      recordVideoInfo: undefined,
      regResult: undefined, //视频服务注册后结果集
      iframeUrl: '', //访问H5版本前端视频组件地址
      publicSecurityCofig: '', // 公安校验配置
      appNativeVideoConfig: {
        cwAuthCode: '', //云从授权码
        aliYunUrl: '', //阿里云地址
        aliYunKey: '', //阿里云Key
        aliYunAsrUrl: '',
        aliYunToken: '' //阿里云Token
      }
    };
  },
  props: {
    liveErrorMsg: {
      type: String,
      default:
        '由于人脸认证多次失败，请录制一段5~10秒钟的视频，完成本次业务办理。'
    },
    smartErrorMsg: {
      type: String,
      default:
        '由于视频录制多次失败，请录制一段5~10秒钟的视频，完成本次业务办理。'
    },
    temNo: {
      type: String,
      default: ''
    },
    recordNtnText: {
      type: String,
      default: '立即录制'
    }
  },
  watch: {},
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    },
    bizType() {
      const { inProperty } = this.tkFlowInfo();
      return inProperty.bizType;
    },
    maxErrorMsg() {
      if (this.liveFailCount >= this.liveFailCountMax) {
        return this.liveErrorMsg;
      } else {
        return this.smartErrorMsg;
      }
    },
    // 公安校验类型
    publicSecurityType() {
      // interfaceNo公安接口标识 1001：中登返照，1002：中登人脸比对，1003：证通人像比对
      //1002,1003需要向视频服务注册证件人像面
      return this.publicSecurityCofig.interfaceNo
        .split(',')
        .some((a) => ['1002'].includes(a))
        ? '03'
        : '02';
    }
  },
  created() {
    this.pageStep = 0;
    this.loading = true;
    this.startH5 = false;
    this.liveFailCount = 0;
    this.recordFailCount = 0;
    this.eventMessage(this, EVENT_NAME.DISPLAY_HEADER, { display: false });
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },
  mounted() {
    let tips =
      '根据中国证券监督管理委员会、中国证券登记结算有限责任公司、沪深交易所的相关要求，在您本次办理业务时，需向我们提供个人生物识别信息，包括个人视频、个人照片。本次信息采集仅用于业务办理，且仅向相关机构报送必要信息。请确认您已知晓并同意提供以上信息。';
    this.$TAlert({
      title: '温馨提示',
      tips: tips,
      hasCancel: true,
      confirmBtn: '同意',
      cancelBtn: '不同意',
      confirm: () => {
        addClientCritMark({
          flowToken: sessionStorage.getItem('TKFlowToken'),
          markType: '12',
          markContent: tips,
          confirmFlag: '1'
        }).then(() => {
          this.init();
        });
      },
      cancel: this.toIndex
    });
    if (this.isApp) {
      const result = $h.callMessageNative({
        funcNo: '50001'
      });
      result.results = result.results || [];
      let data = result.results[0];
      if (result.error_no == '0' && data) {
        if (!$hvue.iBrowser.ios) {
          this.devicePlatform = data.devicePlatform;
          this.deviceSysVersion = 'Android ' + data.deviceSysVersion;
        } else {
          this.devicePlatform = data.devicePlatform;
          this.deviceSysVersion = 'iOS ' + data.deviceSysVersion;
        }
      }
    }
  },
  destroyed() {
    window.removeEventListener('message', this.iframeCallback, false);
    window.livingRecognitionCallBack = null;
    window.intelligentRecognitionCallBack = null;
  },
  methods: {
    init() {
      const { inProperty } = this.tkFlowInfo();
      let { idCardPortrait } = inProperty;
      securityVerifyConfig({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        idCardPortrait
      })
        .then(({ data }) => {
          this.publicSecurityCofig = data;
          if (this.publicSecurityType === '02') {
            return statusQuery({
              flowToken: sessionStorage.getItem('TKFlowToken'),
              bizType: this.bizType
            });
          } else {
            return {
              data: {
                available: true
              }
            };
          }
        })
        .then(({ data }) => {
          console.info('statusQuery 出参 === ', data);
          if (data.available === true) {
            if (this.isApp) {
              this.loading = false;
              return videoGetScriptV2({
                flowToken: sessionStorage.getItem('TKFlowToken'),
                temNo: this.temNo
              });
            } else {
              return Promise.resolve({});
            }
          } else {
            this.failure();
          }
          return Promise.reject('');
        })
        .then(({ data }) => {
          if (this.isApp) {
            console.info('videoGetScriptV2 出参 === ', data);
            this.questionArray = data.map((it) => {
              return {
                prompt: it.prompt,
                tipTitle: it.showScriptText, // 界面展示话术
                tipContent: it.broadcastScriptContent
                  .replace(/^@|@$/g, '')
                  .replace(/@/g, ''), //语音播报话术
                waitTime: it.waitTime,
                standardans: it.standardans,
                failans: it.failans
              };
            });
          }
          return getConfigMap({
            configKey: 'bc.wjmm.single.count'
          });
        })
        .then(({ data }) => {
          console.info('getConfigMap bc.wjmm.single.count 出参 === ', data);
          try {
            this.liveFailCountMax = Number(Object.values(data)[0].configValue);
          } catch (error) {
            console.error(error);
          }
          return getConfigMap({
            configKey: 'bc.wjmm.video.count'
          });
        })
        .then(({ data }) => {
          console.info('getConfigMap bc.wjmm.video.count 出参 === ', data);
          try {
            this.recordFailCountMax = Number(
              Object.values(data)[0].configValue
            );
            if (!this.isApp) {
              this.start();
            }
          } catch (error) {
            console.error(error);
          }
        })
        .catch((err) => {
          if (err === '') return;
          _hvueToast({ mes: err });
        });
    },
    async start() {
      try {
        const { inProperty } = this.tkFlowInfo();
        let { clientName, idNo, branchNo, idKind, idCardPortrait } = inProperty;
        const {
          data: { id }
        } = await OrderOnTheWay({ bizType: inProperty.bizType });
        console.info('OrderOnTheWay 出参 id === ', id);

        // 生成8位数随机整数
        const randomInt = Math.floor(10000000 + Math.random() * 90000000);
        const newId = `${id}${randomInt}`; // 拼接随机整数

        this.eventMessage(this, 'closeReject');

        //获取视频服务token，用于单向视频注册免冠照
        const jwt = await videoGetJwtToken({
          businessType: inProperty.bizType,
          flowNo: newId // 使用拼接后的新ID
        });
        console.info('videoGetJwtToken 出参 === ', jwt);
        let jwtToken = jwt.data.jwtToken;
        $h.setSession('videoToken', jwtToken);
        this.regResult = { jwtToken };

        let result;
        //1002需要向视频服务注册证件人像面
        if (this.publicSecurityType === '03') {
          const param = {
            imgUrl: idCardPortrait,
            jwtToken
          };
          console.info('video/addAuthFace 入参 === ', param);
          result = await videoOneRegist(param);
          console.info('video/addAuthFace 出参 === ', result);
        } else {
          // 将免冠照注册到视频服务，用于后续人脸比对
          const param = {
            jwtToken,
            idKind,
            branchNo,
            idNo,
            clientName
          };
          console.info('videoAddCertificate 入参 === ', param);
          result = await videoAddCertificate(param);
          console.info('videoAddCertificate 出参 === ', result);
        }
        if (result.code == 0) {
          this.regResult = { ...this.regResult, flowNo: result.data.flowNo };
          if (this.isApp) {
            const nativeConfig = await getAppNativeVideoConfig({});
            console.info('getAppNativeVideoConfig 出参 === ', nativeConfig);
            this.appNativeVideoConfig = { ...nativeConfig.data };
            this.startAppLiving();
          } else {
            this.startH5Living();
            this.loading = false;
          }
        } else {
          _hvueToast({
            mes: result.msg
          });
        }
      } catch (e) {
        _hvueToast({
          mes: e
        });
      }
    },
    getRandAction(num) {
      var actionArr = ['0', '1', '6', '7']; // 云从动作 0:眨眼 1:上下点头 6:向左摇头 7:向右摇头
      var resultArr = [];
      if (!num) num = 1;
      if (num > actionArr.length) num = actionArr.length;
      for (var i = 0; i < num; i++) {
        var rand = Math.floor(Math.random() * actionArr.length);
        resultArr.push(actionArr.splice(rand, 1));
      }
      let nameArr = [];
      let nameMap = {
        0: 'blink',
        1: 'nod ',
        6: 'yaw',
        7: 'yaw'
      };
      resultArr.forEach((a) => {
        nameArr.push(nameMap[a]);
      });
      return {
        num: resultArr.join(','),
        name: nameArr.join('|')
      };
    },
    startAppLiving() {
      window.livingRecognitionCallBack = this.livingRecognitionCallBack;
      let actionGroup = this.getRandAction(1);
      this.livingLogParam = {
        actionType: actionGroup.name
      };
      let param = {
        funcNo: '60007',
        moduleName: $hvue.customConfig.moduleName,
        moreMaxFailNum: this.liveFailCountMax,
        actionGroup: actionGroup.num,
        mainColor: $hvue.customConfig.mainColor,
        cwAuthCode: this.appNativeVideoConfig.cwAuthCode
      };
      const nativeRes = $h.callMessageNative(param);
      console.info('60007callMessageNative ==== ', nativeRes);
    },
    startH5Living() {
      isSupportWebRTC(({ error_no = '' }) => {
        if (error_no !== 0) {
          this.$TAlert({
            title: '温馨提示',
            tips: '当前浏览器不支持视频见证，请更换浏览器或联系客服电话95310',
            confirm: this.failure
          });
        } else {
          this.iframeUrl = window.location.origin + '/auth-living-view/views/index.html';
          this.startH5 = true;
          this.videoIframeShow = true;
          window.addEventListener('message', this.iframeCallback, false);
        }
      });
    },
    setLiveErrorMsg(errorNo, errorInfo) {
      console.info('setLiveErrorMsg ==== ', { errorNo, errorInfo });
      const errData = {
        '-2': [
          '活体识别未通过',
          '由于长时间活体识别未通过，本次人脸认证失败，请重新识别'
        ],
        '-999': ['人脸识别不通过', '自拍照与证件照不是同一人']
      };
      if (this.liveFailCount >= this.liveFailCountMax) {
        this.pageStep = 3;
      } else {
        this.liveFailCount++;
        this.pageStep = 2;
        if (errData[errorNo]) {
          this.livingErrorParam = {
            title: errData[errorNo][0],
            context: errData[errorNo][1]
          };
        } else {
          this.livingErrorParam = {
            context: errorInfo
          };
        }
      }
    },
    setRecordErrorMsg() {
      console.info('setRecordErrorMsg ==== ', { errorNo, errorInfo });
      const errData = {}; //todo 待联调完成后补充
      this.recordFailCount++;
      this.pageStep = 4;
      if (errData[errorNo]) {
        this.livingErrorParam = {
          title: errData[errorNo][0],
          context: errData[errorNo][1]
        };
      } else {
        this.livingErrorParam = {
          context: errorInfo
        };
      }
    },
    livingRecognitionCallBack(data) {
      console.info('livingRecognitionCallBack === ', data);
      if (data.error_no == '-1') {
        console.log('取消');
        this.pageStep = 0;
      } else if (data.error_no != '0') {
        this.setLiveErrorMsg(data.error_no, data.error_info);
      } else {
        this.pageStep = 5;
        this.livingImageBase64 = data.actionBase64 || data.base64;
        try {
          submitLivingLog(
            {
              livingImageData: filterBase64Pre(
                data.actionBase64 || data.base64
              ),
              ...this.livingLogParam
            },
            { loading: false }
          )
            .then((res) => {
              console.info('submitLivingLog 出参 === ', data);
              if (res.code == 0) {
                const param = {
                  flowNo: this.regResult.flowNo,
                  faceImageData: filterBase64Pre(data.base64)
                };
                console.info('faceLivingFaceCompareApp 入参 === ');
                return faceLivingFaceCompareApp(param, { loading: false });
              } else {
                return Promise.reject(new Error(res.msg));
              }
            })
            .then(({ data, code, msg }) => {
              console.info('faceLivingFaceCompareApp 出参 === ', data);
              if (code === 0) {
                if (data.pass) {
                  this.pageStep = 4;
                } else {
                  this.setLiveErrorMsg(-999, data.error_info);
                }
              } else {
                return Promise.reject(new Error(msg));
              }
            })
            .catch((e) => {
              if (e.message == 'Network Error') {
                this.setLiveErrorMsg(null, '当前网络已断开');
              } else this.setLiveErrorMsg(null, e.message);
              // _hvueAlert({ mes: e.message });
            });
        } catch (err) {
          console.log(err);
        }
      }
    },
    intelligentRecognitionCallBack(data) {
      console.info('intelligentRecognitionCallBack === ', data);
      if ([-1, '-1', -8, '-8'].includes(data.error_no)) {
        console.log(data.error_info);
        this.pageStep = 4;
      } else if (data.error_no != '0') {
        this.recordFailCount = this.recordFailCountMax;
        this.pageStep = 3;
        /* if (this.recordFailCount >= this.recordFailCountMax) {
          this.pageStep = 3;
        } else {
          this.setRecordErrorMsg(data.error_no, data.error_info);
        } */
      } else {
        this.videoUploadLoading = true;
        this._uploadVideo({
          videoBase64: data.videoBase64,
          videoLength: data.video_length
        });
      }
    },
    record() {
      if (!this.isApp) {
        this.start();
        return;
      }
      window.intelligentRecognitionCallBack =
        this.intelligentRecognitionCallBack;
      const param = {
        funcNo: '60026',
        url: `${$hvue.customConfig.video.videoServer}/auth-common-server/servlet/json`,
        requestHeaders: {
          'tk-jwt-authorization': $h.getSession('videoToken')
        },
        faceDetectInterval: '1', // 检测间隔
        faceDetectFuncNo: '15000000', // 人脸在框检测功能号
        faceDetectCompositeFuncNo: '15000006', // 人脸检测综合功能号
        questionArray: JSON.stringify(this.questionArray),
        maxFailureCountPerFaceDetect: this.recordFailCountMax, //每次录制过程人脸离框最大次数
        maxFailureCountPerFaceCompare: this.recordFailCountMax, //每次录制过程人脸比对失败最大次数
        totalFaceDetectFailureCount: this.recordFailCountMax, //最大人脸检测失败录制次数
        totalFaceCompareFailureCount: this.recordFailCountMax, //最大人脸对比失败录制次数
        aliYunUrl: this.appNativeVideoConfig.aliYunUrl, //阿里nui语音服务器地址
        aliYunAsrUrl: this.appNativeVideoConfig.aliYunAsrUrl, //阿里语音识别服务器地址
        aliYunKey: this.appNativeVideoConfig.aliYunKey, //阿里云授权Key
        aliYunToken: this.appNativeVideoConfig.aliYunToken //通过阿里云授权的KeyId和Secret生成的token
      };
      console.info('60026callMessageNative 入参 ==== ', param);
      const nativeRes = $h.callMessageNative(param);
      console.info('60026callMessageNative 出参 ==== ', nativeRes);
    },
    iframeLoad() {
      const { inProperty } = this.tkFlowInfo();
      let { clientName } = inProperty;
      if (this.iframeUrl !== '') {
        this.$refs.iframe.contentWindow.postMessage(
          {
            initParam: {
              no_title: 1,
              authorization: this.regResult.jwtToken,
              flow_no: this.regResult.flowNo,
              biz_type: this.bizType,
              auth_type: 'authVideoRecordAuto_zg', //authVideoRecordAuto_zg即构智能播报单向
              main_color: $hvue.customConfig.mainColor,
              tem_no: this.temNo,
              replace_params: [
                {
                  paramName: 'clientName',
                  paramValue: clientName // 将中台配置话术的${name}替换
                }
              ],

              face_fail_num: this.liveFailCountMax,
              live_action_total_fail_num: this.liveFailCountMax,

              record_face_total_fail_num: this.recordFailCountMax,
              record_face_compare_total_fail_num: this.recordFailCountMax,
              record_replay_fail_num: this.recordFailCountMax,
              record_no_sound_total_fail_num: this.recordFailCountMax,

              explain_title: '请录制一段5~10秒的视频',
              explain_tips:
                '请以清晰话术回答视频中的问题，我们将根据您的回答确认您的意愿'
            }
          },
          this.iframeUrl
        ); //子页面地址
      }
    },
    /**
     * @description H5单向前端工程回调函数
     * @param {Object} data.authResult 返回参数
     * @param {String} data.authResult.error_no 认证结果错误码 0表示正常通过 1表示超出错误次数跳转老版本单向 -1表示用户主动返回 -2表示认证码过期
     * @param {String} data.authResult.error_info 结果说明
     */
    iframeCallback(event) {
      // 接收子页面回传的结果
      console.info('iframeCallback =======', event);
      try {
        const result = event.data.authResult || {};
        if (result.error_no === 0) {
          this.queryVideoResult();
        } else if ([1].includes(result.error_no)) {
          this.failure();
        } else if ([-1, -2, -3].includes(result.error_no)) {
          this.toIndex();
        }
      } catch (e) {
        console.error(e);
      }
    },
    failure() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        is_witness_pass: '0'
      });
    },
    back() {
      if (this.pageStep === 0) {
        this.toIndex();
      } else {
        this.pageStep = 0;
      }
    },
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    queryVideoResult() {
      // this.startH5 = false;
      faceLivingResult({}).then(({ data, code, msg }) => {
        console.info('faceLiving/result 出参 === ', data);
        if (code == 0) {
          //livingImageId活体关键帧 faceImageId大头照照片都保存了，前端判断为活体识别，智能录制流程已通过。
          if (
            data.livingImageId !== '' &&
            data.faceImageId !== '' &&
            data.videoFileId !== ''
          ) {
            let formData = {
              witness_video: data.videoFileId,
              is_witness_pass: '1',
              witness_way: '1' //0双向 1单向
            };
            this.recordVideoInfo = { fileKey: formData.witness_video };
            this.pageStep = 7;
            // this.eventMessage(this, EVENT_NAME.NEXT_STEP, formData);
            this.videoIframeShow = false;
          } else {
            this.pageStep = 4;
            this.videoIframeShow = false;
          }
        } else {
          this.$TAlert({ tips: msg });
        }
      });
    },
    _uploadVideo({ videoBase64, videoLength }) {
      console.log('确认提交 start');
      let _this = this;
      let param = {
        videoBase: filterBase64Pre(videoBase64),
        videoExt: 'mp4'
      };
      const listner = {
        success: (data) => {
          console.info('_uploadVideo ===== ', data);
          if (data.code === 0) {
            this.recordVideoInfo = { ...data.data, videoLength };
            this.pageStep = 7;
          } else {
            _this.videoUploadLoading = false;
            _this.pageStep = 4;
            console.error(data.msg);
          }
        },
        error: (err) => {
          _this.videoUploadLoading = false;
          _this.pageStep = 4;
          console.error(err);
        },
        progress: (p) => {
          _this.pageStep = 6;
          _this.progressCount = p;
        }
      };
      uploadVideo({
        url: $hvue.customConfig.serverUrl + '/video/upload',
        param,
        listner
      });
    },
    next() {
      const { fileKey, videoLength } = this.recordVideoInfo;
      let formData = {
        witness_video: fileKey,
        // video_size: this.fileData.size,
        video_length: videoLength,
        is_witness_pass: '1',
        witness_way: '1' //0双向 1单向
      };
      _hvueLoading.close();
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, formData);
    }
  }
};
</script>

<style scoped>
.fc_basebox {
  background: #fff;
  padding: 0.35rem 0.15rem 0.1rem;
  text-align: center;
  font-size: 0.14rem;
  line-height: 0.2rem;
  color: #999999;
}
.fc_basebox h5 {
  font-size: 0.24rem;
  line-height: 0.28rem;
  font-weight: normal;
  margin-bottom: 0.1rem;
  color: #000000;
}
.fc_basebox .pic {
  margin-top: 0.3rem;
}
.fc_basebox .pic img {
  display: block;
  height: 3rem;
  margin: 0 auto;
}
</style>
