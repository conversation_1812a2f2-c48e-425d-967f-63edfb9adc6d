<template>
  <section
    class="main fixed white_bg_sc"
    data-page="home"
    style="position: fixed"
  >
    <t-header></t-header>
    <article class="content">
      <div class="white_bg">
        <div class="top_searchbox">
          <i class="icon"></i>
          <input
            v-model="searchKey"
            class="t1"
            type="text"
            maxlength="25"
            style="padding: 0.08rem 0.15rem 0.08rem 0.4rem"
            placeholder="请输入基金公司名称"
            @keyup.enter="toSearch"
          />
          <a class="btn" @click="toSearch">搜索</a>
        </div>
      </div>
      <div
        v-if="
          showAccountList.length === 0 &&
          showOpenAccountList.length === 0 &&
          loadingEd
        "
        class="nodata_box"
      >
        <div class="icon"><img src="@/assets/images/noData.svg" /></div>
        <p>很抱歉，没有搜到结果</p>
      </div>
      <div v-if="loadingEd && showAccountList.length > 0" class="com_title">
        <h5>请选择您想要开通的基金公司账户</h5>
        <span
          class="icon_check check_all"
          :class="{ checked: checkedAll }"
          @click="chooseAll"
          >全选</span
        >
      </div>
      <!-- <div v-if="canOpenList.length === 0 && loadingEd" class="reject_box">
        <h5 class="no_arrow">
          <span>暂没有可勾选开通的账户，无法开户。</span>
        </h5>
      </div> -->
      <div
        class="fund_acct_scroll"
        style="-webkit-overflow-scrolling: touch"
        :class="{ noAccount: canOpenList.length === 0 }"
      >
        <div
          v-for="(item, index) in fundCompany"
          v-show="item.data && item.data.filter((itx) => itx.show).length !== 0"
          :key="index + 'fundCompany'"
          class="fund_acct_item"
        >
          <h5 class="letter" style="background: #f5f6fa">{{ item.Pinyin }}</h5>
          <ul class="acct_list">
            <li
              v-for="(it, idx) in item.data"
              v-show="it.show"
              :key="idx + 'fundCompanydata'"
              @click="chooseCompany(it.fundCompany, it.processing)"
            >
              <span
                class="icon_check"
                :class="{ checked: it.checked, disabled: it.processing }"
                >{{ it.fundCompanyName }}</span
              >
              <span v-if="it.processing" class="state">处理中</span>
            </li>
          </ul>
        </div>

        <div
          v-if="loadingEd && showOpenAccountList.length > 0"
          class="com_title imp_bg"
        >
          <h5>已开通的账户如下</h5>
        </div>
        <div
          v-for="(item, index) in openFundAccount"
          v-show="item.data && item.data.filter((itx) => itx.show).length !== 0"
          :key="index + 'openFundAccount'"
          class="fund_acct_item"
        >
          <h5 class="letter" style="background: #f5f6fa">{{ item.Pinyin }}</h5>
          <ul class="acct_list">
            <li
              v-for="(it, idx) in item.data"
              v-show="it.show"
              :key="idx + 'openFundAccountdata'"
            >
              <span class="icon_check disabled">{{ it.fundCompanyName }}</span>
              <span class="state">{{ it.fundCompanyAccount }}</span>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer">
      <div v-if="canOpenList.length > 0" class="ce_btn">
        <a
          class="p_button"
          :class="{
            disabled:
              openAccList.length === 0 ||
              showAccountList.length === 0 ||
              showOpenSelectedList.length === 0
          }"
          @click="toNext()"
          >马上开通</a
        >
      </div>
      <div v-if="canOpenList.length === 0 && loadingEd" class="ce_btn">
        <a class="p_button" @click="toIndex()">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import {
  fundCompanyList,
  openFundAccountQry,
  processingFundAccountQry
} from '@/service/service';
import THeader from '@/components/THeader.vue';

export default {
  name: 'FundCompanySelection',
  components: { THeader },
  inject: ['eventMessage'],
  data() {
    return {
      // checkedAll: false,
      allList: [],
      fundCompany: [],
      openFundAccount: [],
      processingCompany: [],
      canOpenList: [],
      loadingEd: false,
      searchKey: ''
    };
  },
  computed: {
    checkedAll() {
      if (!this.loadingEd) {
        return false;
      }
      // 判断当前显示的所有基金公司是否都是勾选状态
      let flag =
        this.fundCompany.length > 0 &&
        this.loadingEd &&
        this.fundCompany.some((data) => {
          return (
            data.data.filter((item) => {
              return item.show && !item.checked;
            }).length > 0
          );
        });
      if (!flag) {
        return true;
      } else {
        return false;
      }
    },
    showOpenSelectedList() {
      let arr = [];
      if (!this.loadingEd) {
        return arr;
      } else {
        for (let data of this.fundCompany) {
          for (let item of data.data) {
            if (item.show && item.checked) {
              arr.push(item.fundCompany);
            }
          }
        }
        return arr;
      }
    },
    showAccountList() {
      let arr = [];
      if (!this.loadingEd) {
        return arr;
      } else {
        for (let data of this.fundCompany) {
          for (let item of data.data) {
            if (item.show) {
              arr.push(item.fundCompany);
            }
          }
        }
        return arr;
      }
    },
    showOpenAccountList() {
      let arr = [];
      if (!this.loadingEd) {
        return arr;
      } else {
        for (let data of this.openFundAccount) {
          for (let item of data.data) {
            if (item.show) {
              arr.push(item.fundCompany);
            }
          }
        }
        return arr;
      }
    },
    openAccList() {
      let arr = [];
      if (!this.loadingEd) {
        return arr;
      } else {
        for (let data of this.fundCompany) {
          for (let item of data.data) {
            if (item.checked) {
              arr.push(item.fundCompany);
            }
          }
        }
        /* if (arr.length > 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 1 });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
        } */
        return arr;
      }
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.renderingView();
  },
  methods: {
    toIndex() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    toSearch() {
      for (let data of this.fundCompany) {
        for (let item of data.data) {
          if (item.fundCompanyName.includes(this.searchKey)) {
            this.$set(item, 'show', true);
          } else {
            this.$set(item, 'show', false);
          }
        }
      }
      for (let data of this.openFundAccount) {
        for (let item of data.data) {
          if (item.fundCompanyName.includes(this.searchKey)) {
            this.$set(item, 'show', true);
          } else {
            this.$set(item, 'show', false);
          }
        }
      }
    },

    toNext() {
      let arr = [];
      for (let data of this.fundCompany) {
        for (let item of data.data) {
          if (item.checked && item.show) {
            arr.push(item.fundCompany);
          }
        }
      }
      if (arr.length > 0 && this.showAccountList.length > 0) {
        this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
          fundCompanyJson: this.openAccList.join(',')
        });
      } else {
        return;
      }
    },

    renderingView() {
      const { fund_company = '' } = $h.getSession('pageParams') || {};
      fundCompanyList({
        queryType: this.$attrs.queryType ? '1' : '0'
      }).then((res) => {
        // this.fundCompany = this.sortByPinYing(res.data);
        // https://jira.gjzq.cn/browse/KFSBYWBLCG-9026 一键下单支持办理开通TA账户改造
        this.fundCompany = res.data;
        if (fund_company !== '') {
          let fundCompanyJson = [];
          for (let { fundCompany } of this.fundCompany) {
            if (fundCompany === fund_company) {
              fundCompanyJson.push(fundCompany);
            }
          }
          if (fundCompanyJson.length > 0) {
            this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
              fundCompanyJson: fundCompanyJson.join(',')
            });
            return;
          }
        }
        processingFundAccountQry().then((ingRes) => {
          // this.processingCompany = ingRes.data.openFundCompany.split(',');
          openFundAccountQry().then((underRes) => {
            this.openFundAccount = this.sortByPinYing(underRes.data);
            this.fundCompany = this.fundCompany.filter((item) => {
              let arr = underRes.data.map((it) => it.fundCompany);
              if (arr.includes(item.fundCompany)) {
                return false;
              } else {
                return true;
              }
            });
            // this.fundCompany.forEach((item) => {
            //   if (this.processingCompany.includes(item.fundCompany)) {
            //     this.$set(item, 'processing', true);
            //   } else {
            //     this.$set(item, 'processing', false);
            //   }
            // });
            this.fundCompany = this.sortByPinYing(this.fundCompany);
            for (let data of this.fundCompany) {
              for (let item of data.data) {
                if (!item.processing) {
                  this.canOpenList.push(item.fundCompany);
                }
              }
            }
            console.log(this.fundCompany);
            this.loadingEd = true;
          });
        });
      });
    },

    chooseAll() {
      // this.checkedAll = !this.checkedAll;
      if (this.checkedAll) {
        for (let data of this.fundCompany) {
          for (let item of data.data) {
            if (item.processing) continue;
            if (item.show) {
              this.$set(item, 'checked', false);
            }
          }
        }
      } else {
        for (let data of this.fundCompany) {
          for (let item of data.data) {
            if (item.processing) continue;
            if (item.show) {
              this.$set(item, 'checked', true);
            }
          }
        }
      }
      this.$emit('change', {
        fundCompanyJson: this.openAccList.join(',')
      });
    },

    chooseCompany(fundCompany, disabled) {
      if (disabled) {
        return;
      }
      for (let data of this.fundCompany) {
        for (let item of data.data) {
          if (item.fundCompany === fundCompany) {
            if (item.checked) {
              this.$set(item, 'checked', false);
            } else {
              this.$set(item, 'checked', true);
            }
            break;
          }
        }
      }
      this.$emit('change', {
        fundCompanyJson: this.openAccList.join(',')
      });
    },

    sortByPinYing(arr) {
      let returnArr = [];
      let pinYinArr = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ];
      pinYinArr.forEach((item) => {
        let optionArr = [];
        arr.forEach((it) => {
          let firstStr = it.fundCompanyFirstPinyin.charAt(0);
          if (firstStr === item) {
            optionArr.push({ ...it, show: true });
          }
        });
        if (optionArr.length > 0) {
          returnArr.push({
            Pinyin: item,
            data: optionArr
          });
        }
      });
      return returnArr;
    }
  }
};
</script>

<style scoped>
@media (min-aspect-ratio: 13/20) {
  .p_button {
    display: none;
  }
}
.noAccount {
  max-height: calc(100% - 0.48rem) !important;
}
</style>
