<template>
  <fragment>
    <!-- 原存管银行的相关信息 -->
    <div class="old_bank_info" v-if="$attrs.showOldBankInfo">
      <div class="com_title">原存管银行</div>
      <van-field
        label="存管银行"
        v-model="oldBankInfo.bankName"
        label-width="5em"
        readonly
      />
      <van-field
        label="银行卡号"
        v-model="oldBankInfo.bankAccount"
        label-width="5em"
        readonly
      />
      <div class="input_form spel">
        <div v-if="needOldPassword" class="input_text text">
          <span class="tit active">{{
            passwordType[this.oldBankInfo.cancelPsdInd]
          }}</span>
          <h-keypanel
            v-model="oldBankPassWord"
            type="tel2"
            class="t1"
            maxlength="6"
            :mask="true"
            :is-head-icon="true"
            extra-parent-el=".hui-flexview"
            :placeholder="
              '请输入' + passwordType[this.oldBankInfo.cancelPsdInd]
            "
            @on-show="showKeypanel"
          >
            <div slot="head" class="safe-head">
              <img src="@/assets/images/logo.png" alt="" />
              <span>佣金宝安全输入</span>
            </div>
          </h-keypanel>
        </div>
      </div>
    </div>
    <!-- 绑定新村管银行的相关信息 -->
    <div class="new_bank">
      <div class="com_title">绑定新存管银行</div>
      <div class="input_form spel">
        <div class="input_text text">
          <span class="tit">银行卡号</span>
          <input
            ref="bkaccountNewRef"
            v-model="bkaccountNew"
            class="t1"
            type="tel"
            placeholder="输入银行卡号"
            maxlength="19"
            @input="bankCardInputFormat"
          />
          <a class="icon_photo" @click="ocrParseBankCard" />
        </div>
        <div class="input_text text">
          <span class="tit active">所属银行</span>
          <div
            class="dropdown"
            placeholder="请选择签约银行"
            @click="showPickerFn"
          >
            <div v-if="bankFullName">
              <img
                :src="`data:image/jpeg;base64,${selectBankData.bankLogo}`"
              />{{ bankFullName }}
            </div>
          </div>
        </div>
        <div class="input_text text" v-if="needNewPassword">
          <div class="input_text text">
            <span class="tit active">{{
              newPasswordType[this.selectBankData.passwordType]
            }}</span>
            <h-keypanel
              v-model="bkPasswordNew"
              ref="keyPanel"
              type="tel2"
              class="t1"
              maxlength="6"
              :mask="true"
              :is-head-icon="true"
              extra-parent-el=".hui-flexview"
              :placeholder="
                '请输入' + newPasswordType[this.selectBankData.passwordType]
              "
              @on-show="showKeypanel"
            >
              <div slot="head" class="safe-head">
                <img src="@/assets/images/logo.png" alt="" />
                <span>佣金宝安全输入</span>
              </div>
            </h-keypanel>
          </div>
        </div>
      </div>
    </div>
    <div class="warm_tips" v-show="warmTip">{{ warmTip }}</div>
    <agreement-sign
      v-if="selectBankData.bankNo"
      v-model="isAgreeChecked"
      :group-id="$attrs.groupId"
      :isCredit="$attrs.isCredit"
      :bank-id="selectBankData.bankNo"
      :contract-type="$attrs.contractType"
      :selectBankData="selectBankData"
      @agree-list="agreeCallback"
    />
    <bank-view-list :bankList="columns" />
    <van-popup v-model="showPicker" round position="bottom">
      <bank-picker
        v-model="selectBankData"
        type="bank"
        :columns="columns"
        @cancel="showPicker = false"
      />
    </van-popup>
    <getImgBoxApp ref="getImgBoxApp" @getImgCallBack="getImgCallBack" />
    <get-img-box-browser
      ref="getImgBoxBrowser"
      :scan="true"
      @getImgCallBack="getImgCallBack"
    />
    <!-- 留痕弹层 -->
    <van-overlay :show="showConfirm" z-index="999">
      <div class="wrapper">
        <div class="block">
          <div class="content">
            该业务办理需采集您的银行卡信息(视银行规定)，用于存管银行与资金账户的绑定。请确认同意提交以上信息。
          </div>
          <div class="btns">
            <div class="left-btn" @click="back()">取消</div>
            <div class="right-btn" @click="loggerEvent()">同意</div>
          </div>
        </div>
      </div>
    </van-overlay>
  </fragment>
</template>

<script>
import bankViewList from './components/BankViewList';
import BankPicker from '@/components/BankPicker';
import { EVENT_NAME } from '@/common/formEnum';
import agreementSign from './components/AgreementSign';
import getImgBoxBrowser from '@/components/getImg_browser';
import getImgBoxApp from './components/getImg_app';
import { uploadFile, getPwdEncryption, signAgreeV2 } from '@/common/util';
import { getBank, binquery, addClientCritMark } from '@/service/service';

export default {
  name: 'BindBankV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {
    bankViewList,
    getImgBoxBrowser,
    agreementSign,
    BankPicker,
    getImgBoxApp
  },
  data() {
    return {
      oldBankInfo: {},
      oldBankPassWord: '',
      fileUrl: $hvue.customConfig.fileUrl, //银行logo地址
      selectBankData: {}, // 选中的银行信息
      columns: [], // 银行列表
      showPicker: false,
      isAgreeChecked: false,
      agreeList: [],
      bkaccountNew: '', // 银行卡号
      bankFullName: '', // 所属银行名称
      bkPasswordNew: '', // 银行密码
      bankNoNew: '', // 银行密码
      isShowNewPwd: false,
      isShowoldPwd: false,
      dcType: '', // 银行卡类型 0 借记卡；1 信用卡
      passwordType: {
        1: '取款密码',
        2: '手机密码',
        3: '查询密码'
      },
      newPasswordType: {
        8: '取款密码',
        9: '手机密码',
        10: '查询密码'
      },
      showConfirm: true,
      bindStatus: '', // 存管银行流程状态 1-新开；2-变更；3-绑卡
      warmTip: '',
      cardBinResult: {},
      canAddClientCritMark: true // 埋点防重点击
    };
  },
  computed: {
    needOldPassword() {
      return (
        this.$attrs.showOldBankInfo &&
        this.oldBankInfo.cancelPsdInd !== '0' &&
        ['1', '2', '3'].includes(this.oldBankInfo.cancelPsdInd)
      );
    },
    needNewPassword() {
      return (
        this.selectBankData.needPassword &&
        this.selectBankData.needPassword == '1' &&
        ['8', '9', '10'].includes(this.selectBankData.passwordType)
      );
    },
    watchObj() {
      let {
        oldBankPassWord,
        bkaccountNew,
        bankFullName,
        bkPasswordNew,
        isAgreeChecked
      } = this;
      return {
        oldBankPassWord,
        bkPasswordNew,
        bkaccountNew,
        isAgreeChecked,
        bankFullName
      };
    }
  },
  watch: {
    watchObj: function (val, oldVal) {
      let {
        oldBankPassWord,
        bkaccountNew,
        bkPasswordNew,
        isAgreeChecked,
        bankFullName
      } = val;
      let goOn = '';
      if (this.needOldPassword) {
        goOn = Boolean(
          oldBankPassWord && bkaccountNew && isAgreeChecked && bankFullName
        );
      }
      if (this.needNewPassword) {
        goOn = Boolean(
          bkPasswordNew && bkaccountNew && isAgreeChecked && bankFullName
        );
      }
      if (this.needNewPassword && this.needOldPassword) {
        goOn = Boolean(
          oldBankPassWord &&
            bkPasswordNew &&
            bkaccountNew &&
            isAgreeChecked &&
            bankFullName
        );
      }
      if (!this.needNewPassword && !this.needOldPassword) {
        goOn = Boolean(bkaccountNew && isAgreeChecked && bankFullName);
      }
      if (goOn) {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
          btnStatus: 2,
          data: () => {
            this.goNext();
          }
        });
      } else {
        this.eventMessage(this, EVENT_NAME.NEXT_BTN, { btnStatus: 0 });
      }
    },
    selectBankData: function (val, oldVal) {
      const { bindType, bankNo, bankName } = val;
      if (this.$attrs.isCredit) {
        if (bindType == '2') {
          this.warmTip =
            '温馨提示: 请在签约成功后登录银行网银完成三方关联，详见官网常见问题。';
        } else if (bankNo == '7' || bankNo === 'K' || bankNo === 'J') {
          this.warmTip =
            '温馨提示: 签约成功后，第一笔转账须由银行端发起，详见官网常见问题。';
        } else {
          this.warmTip = '';
        }
      } else {
        if (bindType == '2') {
          this.warmTip =
            '温馨提示: 请在签约成功后登录银行网银完成三方关联，详见官网常见问题。';
        } else if (bankNo == '7' || bankNo === 'K') {
          this.warmTip =
            '温馨提示: 签约成功后，第一笔转账须由银行端发起，详见官网常见问题。';
        } else if (bankNo === 'J') {
          this.warmTip =
            '温馨提示: 请在签约成功后登录银行网银激活三方存管，详见官网常见问题。';
        } else {
          this.warmTip = '';
        }
      }
      if (bankNo === oldVal.bankNo) {
        return;
      }
      this.bankFullName = bankName;
      this.bankNoNew = bankNo;
      this.bkPasswordNew = '';
      if (this.$refs.keyPanel) {
        this.$refs.keyPanel.setValue(this.bkPasswordNew);
      }
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
      text: '下一步',
      btnStatus: 0
    });
    this.getBankList();
    if (this.tkFlowInfo().inProperty.extInitParams) {
      const { bindStatus, oldBankInfo } = JSON.parse(
        this.tkFlowInfo().inProperty.extInitParams
      );
      if (bindStatus) {
        this.bindStatus = bindStatus;
      }
      if (oldBankInfo) {
        this.oldBankInfo = oldBankInfo;
        this.oldBankInfo.bankAccount =
          oldBankInfo.bkAccountRegflag == 1
            ? ''
            : this.formatBankCardNoFn(this.oldBankInfo.bankAccount);
      }
      if (bindStatus && oldBankInfo) {
        if (bindStatus == '3' && oldBankInfo.bankName) {
          // 绑定银行场景
          this.bankFullName = oldBankInfo.bankName;
          this.bankNoNew = oldBankInfo.bankNo;
          this.selectBankData = oldBankInfo;
        }
      }
    }
  },
  methods: {
    showKeypanel() {
      this.$refs.bkaccountNewRef.blur();
    },
    // 展示银行列表
    showPickerFn() {
      if (this.bindStatus == '3') {
        return;
      }
      this.showPicker = true;
    },
    // 留痕
    loggerEvent() {
      if (this.canAddClientCritMark) {
        this.canAddClientCritMark = false;
      } else {
        return;
      }
      addClientCritMark({
        flowToken: sessionStorage.getItem('TKFlowToken'),
        markContent:
          '该业务办理需采集您的银行卡信息(视银行规定)，用于存管银行与资金账户的绑定。请确认同意提交以上信息。',
        markType: '17',
        confirmFlag: '1'
      })
        .then((res) => {
          console.log('res', res);
          if (res.code == 0) {
            this.showConfirm = false;
          } else {
            _hvueToast({
              mes: '提交失败，请重试。'
            });
            this.canAddClientCritMark = true;
          }
        })
        .catch((err) => {
          _hvueToast({
            mes: '提交失败，请重试。'
          });
          this.canAddClientCritMark = true;
        });
    },
    back() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    selectAgree() {
      this.isAgreeChecked = !this.isAgreeChecked;
    },
    agreeCallback(data) {
      this.agreeList = data;
    },
    // 获取银行列表
    getBankList() {
      let param = {
        counterKind: '0'
      };
      if (this.$attrs.isCredit) param.counterKind = '1';
      getBank({}, param).then((res) => {
        this.columns = res.data.map((item) => {
          item.counterKind = !!item.counterKind
            ? item.counterKind
            : param.counterKind;
          return item;
        });
      });
    },
    // 输入银行卡卡号触发事件
    bankCardInputFormat(e) {
      this.bkaccountNew = e.target.value.replace(/\s/g, '');
      if (this.bkaccountNew.length >= 16 && this.bkaccountNew.length <= 19) {
        this.queryBin();
      }
    },
    // 卡bin查询
    queryBin() {
      let params = {
        cardNo: this.bkaccountNew
      };
      binquery(params)
        .then((res) => {
          console.log('res =====', res);
          if (res.code == 0) {
            const { cardIssuerName, dcType } = res.data;
            this.cardBinResult = res.data;
            let selBankData = this.columns.filter((item) => {
              return cardIssuerName.includes(item.bankName);
            })[0];
            if (selBankData && this.bindStatus != '3') {
              this.bankFullName = selBankData.bankFullName;
              this.bankNoNew = selBankData.bankNo;
              this.selectBankData = selBankData;
            }
            if (!selBankData) {
              this.selectBankData = {};
            }
            this.dcType = dcType;
          }
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    },
    // 银行卡ocr识别
    ocrParseBankCard() {
      if ($hvue.platform == 0) {
        this.$refs.getImgBoxBrowser.getImg();
      } else {
        this.$refs.getImgBoxApp.getImg();
      }
    },
    // 服务端ocr识别 因为银行卡ocr识别功能号会导致客户端闪退 所以目前 端外和端内暂时都用这个----2023.12.22
    getImgCallBack(imgInfo) {
      if (!imgInfo.base64) {
        return;
      }
      _hvueLoading.open();
      uploadFile(
        $hvue.customConfig.serverUrl + '/credit/ocrParseBankCard',
        imgInfo.base64,
        {
          success: async (data) => {
            _hvueLoading.close();
            if (data.code === 0) {
              const { cardNumber, type, bankNo } = data.data;
              this.bkaccountNew = cardNumber.replace(/\s/g, '');
              if (bankNo && bankNo != '') {
                let selBankData = this.columns.filter((item) => {
                  return item.bankNo === bankNo;
                })[0];
                if (selBankData && this.bindStatus != '3') {
                  this.bankFullName = selBankData.bankName;
                  this.bankNoNew = selBankData.bankNo;
                  this.selectBankData = selBankData;
                  this.dcType = type;
                }
                if (!selBankData) {
                  this.selectBankData = {};
                }
              } else {
                this.selectBankData = {};
              }
            } else {
              _hvueAlert({ mes: data.msg });
            }
          },
          progress: (count) => {
            console.log(count);
          },
          error: (e) => {
            _hvueLoading.close();
            console.log(e);
          }
        },
        {}
      );
    },
    // 下一步  继续流程
    async goNext() {
      let {
        bkaccountNew,
        bkPasswordNew,
        bankFullName,
        bankNoNew,
        oldBankPassWord,
        oldBankInfo,
        dcType,
        needOldPassword,
        needNewPassword
      } = this;
      if (
        this.$attrs.showOldBankInfo &&
        this.oldBankInfo.bankNo === this.bankNoNew
      ) {
        this.$TAlert({
          tips: '您的新存管银行与原存管银行相同，线上暂不支持此类变更。您可以拨打客服95310或前往营业部进行办理'
        });
        return;
      }
      try {
        const { data } = await binquery({
          cardNo: this.bkaccountNew
        });
        this.cardBinResult = data;
        if (this.cardBinResult.dcType === '1') {
          this.$TAlert({
            tips: '该银行卡为信用卡，请输入您本人的借记卡进行三方存管绑定。'
          });
          return;
        }
        let regBankNum = /^\d{16,19}$/;
        if (!regBankNum.test(bkaccountNew.replace(/\s/g, ''))) {
          _hvueToast({ mes: '请输入正确格式银行卡卡号' });
          return;
        }
        let regPassword = /^\d{6}$/;
        if (needOldPassword && !regPassword.test(oldBankPassWord)) {
          _hvueToast({ mes: '亲，您输入的密码格式不正确，请重新输入' });
          return;
        }
        if (needNewPassword && !regPassword.test(bkPasswordNew)) {
          _hvueToast({ mes: '亲，您输入的密码格式不正确，请重新输入' });
          return;
        }
        if (
          this.cardBinResult?.cardIssuerName &&
          bankFullName != this.cardBinResult?.cardIssuerName
        ) {
          this.$TAlert({
            tips: '您输入的银行卡号和所选银行可能不匹配，请确认您输入的信息正确，才能成功办理哦。',
            hasCancel: true,
            confirmBtn: '继续办理',
            confirm: () => {
              this.nextStep();
            },
            cancel: () => {
              console.log('no');
            }
          });
        } else {
          this.nextStep();
        }
      } catch (error) {
        console.error('[BindBankV2] 卡BIN查询失败', error);
        let regBankNum = /^\d{16,19}$/;
        if (!regBankNum.test(bkaccountNew.replace(/\s/g, ''))) {
          _hvueToast({ mes: '请输入正确格式银行卡卡号' });
          return;
        }
        let regPassword = /^\d{6}$/;
        if (needOldPassword && !regPassword.test(oldBankPassWord)) {
          _hvueToast({ mes: '亲，您输入的密码格式不正确，请重新输入' });
          return;
        }
        if (needNewPassword && !regPassword.test(bkPasswordNew)) {
          _hvueToast({ mes: '亲，您输入的密码格式不正确，请重新输入' });
          return;
        }
        this.nextStep();
      }
    },
    nextStep() {
      let {
        bkaccountNew,
        bkPasswordNew,
        bankFullName,
        bankNoNew,
        oldBankPassWord,
        oldBankInfo,
        dcType,
        needOldPassword,
        needNewPassword
      } = this;
      const tkFlowInfo = this.tkFlowInfo();
      this.$attrs.agreementNodeNo = this.$attrs.isCredit
        ? 'PBN00041:BNN00071'
        : 'PBN00024:BNN00035';
      this.$attrs.agreementExt = 'bankNo,eleSignAgr';
      let haseEpaperESigned = this.agreeList.filter(
        (item) => item.agreementNo === '100030'
      ).length;
      signAgreeV2(tkFlowInfo, this.agreeList, this.$attrs)
        .then((epaperSignJson) => {
          this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
            epaperSignJson,
            bkaccountNew,
            bankFullName,
            bankNoNew,
            bankNo: oldBankInfo.bankNo || '',
            bkPasswordNew: getPwdEncryption(bkPasswordNew),
            oldBankPassWord: getPwdEncryption(oldBankPassWord),
            epaperESignedFlag: haseEpaperESigned > 0 ? '1' : '0'
          });
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
        });
    },
    formatBankCardNoFn(value) {
      console.log('value', value);
      if (value === '' || value === null || value === undefined) {
        console.log(1);
        return '';
      } else {
        console.log(2);
        let str =
          '**** **** ****' + value.substr(value.length - 4, value.length);
        return str;
      }
    }
  }
};
</script>

<style scoped lang="less">
.dropdown {
  padding-left: 0.8rem;
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.block {
  margin: 0 0.38rem;
  border-radius: 8px;
  background-color: #fff;
}
.content {
  text-align: center;
  padding: 0.24rem;
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
}
.btns {
  display: flex;
  align-items: center;
  justify-content: space-around;
  text-align: center;
  line-height: 56px;
}
.left-btn {
  height: 56px;
  width: 50%;
  font-size: 16px;
  background: rgba(0, 0, 0, 0);
  box-shadow: 0px 0.5px 0px 0px #e5e5e5 inset;
}
.right-btn {
  height: 56px;
  width: 50%;
  color: var(--Primary-B-500, #ff2840);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  background: rgba(0, 0, 0, 0);
  box-shadow: 0px 0.5px 0px 0px #e5e5e5 inset;
}
.warm_tips {
  padding: 0.16rem;
}
</style>
