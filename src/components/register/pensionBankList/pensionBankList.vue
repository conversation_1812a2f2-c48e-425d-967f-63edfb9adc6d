<template>
  <section class="main fixed container" data-page="home">
    <div class="title">
      支持跳转以下银行应用，方便您开立个人养老金银行卡。开通完成后需返回佣金宝APP，继续完成养老金账户开通。
    </div>
    <div class="pension_bank_list" v-for="(item, idx) in pensionBankListArr" :key="idx">
      <div class="left">
        <div class="icon_container">
          <img class="bank_icon" :src="`data:image/jpeg;base64,${item.bankLogo}`" />
        </div>
        <div class="bank_info">
          <div class="bank_name">{{ item.bankName }}</div>
          <div class="market_tip">{{ item.marketTip }}</div>
        </div>
      </div>
      <div class="right" @click="goToBank(item.bankUrl, item.bankNo)">立即开通</div>
    </div>
    <div class="warm_tip">
      温馨提示：开通个人养老金账户需输入养老金银行卡账号信息，请在开通银行卡后记录或截屏保留银行卡号信息再进行养老金账户开通。
    </div>
  </section>
</template>

<script>
import { EVENT_NAME } from '@/common/formEnum';
import { csdcBankQry, getPensionCCBMiniAppParams } from '@/service/service';

export default {
  name: 'pensionBankList',
  inject: ['tkFlowInfo', 'eventMessage'],
  components: {},
  data() {
    return {
      pensionBankListArr: []
    };
  },
  computed: {
    ssoLoginFlag() {
      return this.$store.state.user?.userInfo?.fundAccount;
    }
  },
  watch: {
    ssoLoginFlag: {
      handler: function (fundAccount) {
        console.log(fundAccount);
        if (fundAccount) {
          this.renderingView();
        }
      },
      immediate: true
    }
  },
  created() {
    console.log('test=================');
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
  },

  destroyed() { },
  mounted() { },
  methods: {
    renderingView() {
      console.log('渲染页面');
      csdcBankQry().then((res) => {
        console.log('获取银行列表', res);
        const { code, data } = res;
        if (code == '0') {
          this.pensionBankListArr = data;
        }
      });
    },
    goToBank(bankUrl, bankNo) {
      console.log('bankUrl', bankUrl);
      if ($hvue.platform !== '0') {
        if (bankNo == '005') {
          // 如果是建行
          getPensionCCBMiniAppParams().then(res => {
            const { ftCorpid, ccbParam, accessToken, accEntry } = res.data;
            let path = `pages/FTcommonentry/FTcommonentry?FT_CORPID=${ftCorpid}&ccbParam=${ccbParam}&ACCESS_TOKEN=${accessToken}&ACC_ENTRY=${accEntry}`;
            console.log('瓦片 密钥===', res.data)
            console.log('瓦片 密钥===', path)
            let reqParams = {
              funcNo: '60099',
              actionType: '13',
              params: {
                yjbFuncId: '3027',
                yjbFuncParams: {
                  userName: 'gh_813addf0a4c7',
                  miniProgramType: window.serviceOptions.miniProgramType || 0,
                  path: path,
                }
              }
            };
            const res3027 = $h.callMessageNative(reqParams);
            console.log(`请求结果为: ~~${JSON.stringify(res3027)}`);
          })

        } else if(bankNo == '006'){
          // 如果是交行
          let reqParams = {
              funcNo: '60099',
              actionType: '13',
              params: {
                yjbFuncId: '3027',
                yjbFuncParams: {
                  userName: 'gh_899cdcd47460',
                  miniProgramType: 0, // 统一用正式版
                  path: 'package2/pages/transitPage/index?cnlMerchantNo=WX_GJZQ001&cnlMerchantNme=国金佣金宝APP&ownerBk=***********&signNet=***********&eterNo=EJV0000&actId=1IPGO975E0000C481286000000NT6R9D&eterNo=EJV0000&enterScene=ditui',
                }
              }
            };
            const res3027 = $h.callMessageNative(reqParams);
            console.log(`请求结果为: ~~${JSON.stringify(res3027)}`);
        }else {
          let callMessageNativeParams = {
            funcNo: '50109',
            moduleName: 'open', // 必须为open
            url: bankUrl
          };
          let result = $h.callMessageNative(callMessageNativeParams);
          console.log('返回结果', result);
        }
      } else {
        window.location.href = bankUrl;
      }
    }
  }
};
</script>

<style scoped lang="less">
.container {
  padding: 0.16rem 0.16rem 0;

  .title {
    color: #0f0f1b;
    font-weight: 500;
    line-height: 18px;
    margin-bottom: 0.15rem;
  }

  .pension_bank_list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 0.16rem 0.12rem;
    margin-bottom: 0.12rem;
    min-height: 0.74rem;

    .left {
      display: flex;
      align-items: center;

      .icon_container {
        margin-right: 0.14rem;
        background: #fff;
        margin-right: 12px;
        width: 42px;
        height: 42px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .bank_icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
        }
      }

      .bank_info {
        .bank_name {
          color: #0f0f1b;
          font-size: 16px;
          font-weight: 500;
          line-height: normal;
          margin-bottom: 0.02rem;
        }

        .market_tip {
          color: #87878d;
          font-size: 14px;
          max-width: 180px;
        }
      }
    }

    .right {
      white-space: nowrap;
      padding: 0.04rem 0.12rem;
      color: #ff2840;
      text-align: center;
      font-weight: 500;
      line-height: 20px;
      border-radius: 18px;
      border: 1px solid var(--Primary-B-500, #ff2840);
    }
  }

  .warm_tip {
    color: #55555e;
    line-height: 22px;
    margin-top: 0.16rem;
  }
}
</style>
