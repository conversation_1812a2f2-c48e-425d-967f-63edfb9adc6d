<template>
  <article class="content">
    <div class="com_title">
      <h5>办理此业务需要满足以下条件</h5>
    </div>
    <div class="com_box">
      <ul class="cond_list">
        <template v-for="(item, index) in viewList">
          <li
            v-if="
              item.ruleResult !== '1' && JSON.parse(item.ruleResultDesc).title
            "
            :key="index"
            :class="
              item.userInfo && item.userInfo.warning === '1'
                ? 'warn'
                : item.ruleResult === '1'
                ? 'ok'
                : 'error'
            "
          >
            <div class="tit">
              <h5>
                {{ JSON.parse(item.ruleResultDesc).title }}
                <div class="cond_info_layout" v-if="item.userInfo.markText">
                  <span class="num ared">{{ item.userInfo.markText }}</span>
                </div>
              </h5>
              <p v-if="JSON.parse(item.ruleResultDesc).tips">
                <span v-html="JSON.parse(item.ruleResultDesc).tips" />
                <span class="sfcg_btns">
                  <div
                    v-if="isShowJumpBtnForsfcg()"
                    class="sfcg_btn"
                    @click="
                      goToUrl(
                        item.userInfo.targetUrlYzzz,
                        item.userInfo.targetTypeYzzz,
                        item.userInfo
                      )
                    "
                  >
                    {{ item.userInfo.targetLabelYzzz }}
                  </div>
                  <div
                    v-if="isShowJumpBtnForsfcg()"
                    class="sfcg_btn"
                    @click="
                      goToUrl(
                        item.userInfo.targetUrlSzbxje,
                        item.userInfo.targetTypeSzbxje,
                        item.userInfo
                      )
                    "
                  >
                    {{ item.userInfo.targetLabelSzbxje }}
                  </div>
                </span>
              </p>
              <div class="btn_wrap">
                <!-- <span v-if="item.userInfo.markText" class="num">{{
                  item.userInfo.markText
                }}</span> -->
                <a
                  v-if="item.userInfo.confirmLabel"
                  class="btn"
                  @click="toQuestionSave(item.factorNo)"
                  >{{ item.userInfo.confirmLabel }}</a
                >
                <a
                  v-else-if="item.userInfo.targetTypeCallOut === '1'"
                  class="btn"
                  @click="callOutBtn()"
                  >{{ item.userInfo.targetLabelCallOut }}</a
                >
                <a
                  v-if="isShowJumpBtn(item)"
                  class="btn"
                  @click="
                    toUrl(
                      item.userInfo.targetUrl,
                      item.userInfo.targetType,
                      item.userInfo
                    )
                  "
                  >{{ item.userInfo.targetLabel }}</a
                >
                <a
                  v-if="isShowJumpBtn(item) && !!item.userInfo.targetLabel2"
                  class="btn"
                  @click="
                    toUrl(item.userInfo.targetUrl, item.userInfo.targetType, {
                      bizType: item.userInfo.bizType2,
                      targetLabel: item.userInfo.targetLabel2
                    })
                  "
                  >{{ item.userInfo.targetLabel2 }}</a
                >
              </div>
              <p v-if="checkNormPage(item.propKey)">
                <a class="com_link" @click="showNormFlag = true"
                  >查看认定标准</a
                >
              </p>
            </div>
          </li>
        </template>
      </ul>
    </div>
    <pass-standard v-model="showNormFlag" />
  </article>
</template>

<script>
import PassStandard from '@/components/common/passStandard.vue';
import {
  businessEgliCheck,
  questionSave,
  preCommitAccountCallout
} from '@/service/service.js';
import { RULE_RESULT } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import {
  addUrlParam,
  getOpStation,
  getInstantToken,
  jumpThirdPartyUrl
} from '@/common/util';

export default {
  name: 'EnterCheckV2',
  inject: ['clearKeepAlive', 'eventMessage', 'tkFlowInfo'],
  components: { PassStandard },
  data() {
    return {
      viewList: [],
      bizName: '',
      allPass: false,
      RULE_RESULT,
      pageShow: false,
      showNormFlag: false,
      bizType: ''
    };
  },
  computed: {
    bizType010268() {
      //报价回购权限取消
      return this.bizType === '010268';
    },
    bizType010284() {
      //中签资金预冻结取消
      return this.bizType === '010284';
    },
    bizType010006() {
      //身份证更新
      return this.bizType === '010006';
    }
  },
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.clearKeepAlive();
    window.viewShowCallBack = this.viewShowCallBack;
    let _this = this;
    document.addEventListener(
      'resume',
      function () {
        console.log('resumed');
        _this.renderingView();
      },
      false
    );
    window.onpageshow = (e) => {
      if (e.persisted || window?.performance?.navigation?.type == 2) {
        window.location.reload();
      }
    };
  },
  mounted() {
    // this.renderingView();
    this.$nextTick(() => {
      console.info('==mounted 业务', window.viewShowCallBack);
      this.renderingView();
    });
  },
  destroyed() {
    window.viewShowCallBack = null;
  },
  /*activated() {
    this.renderingView();
  },*/
  // deactivated() {
  //   this.clearKeepAlive();
  // },
  methods: {
    refreshShow() {
      window.addEventListener('pageshow', this.viewShowCallBack);
    },

    viewShowCallBack() {
      console.info('***====****');
      // window.removeEventListener(this.viewShowCallBack);
      this.renderingView();
    },
    toQuestionSave(factorNo) {
      let questionnaireType = 'ageNotMatchPro';
      if (factorNo === 'oftenAddressInfo') {
        questionnaireType = 'addressDoubt';
      }
      questionSave({
        questionnaireType
      }).then((res) => {
        this.renderingView();
      });
    },
    async toUrl(url, type, { bizType, targetLabel }) {
      // 准入跳转业务新跳转方式必须使用push
      if (
        targetLabel === '重新测评' ||
        targetLabel === '更新身份证' ||
        targetLabel === '修改个人资料'
      ) {
        let biz = '';
        if (targetLabel === '重新测评') {
          biz = '010013';
        }
        if (targetLabel === '更新身份证') {
          biz = '010006';
        }
        if (targetLabel === '修改个人资料') {
          biz = '010004';
        }
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, { bizType: biz, initJumpMode: '0' });
        });
        return;
        // let configKey = 'bc.opt.urlback.010013';
        // let { data } = await getConfigMap({
        //   configKey
        // });
        // if (data[configKey].configValue === '0') {
        //   $h.setSession('initJumpMode', '0');
        //   import('@/common/flowMixin.js').then((a) => {
        //     a.initFlow.call(this, '010013');
        //   });
        //   return;
        // }
      }
      if (type === '3') {
        // type 3跳转到介绍页
        this.$router.push({
          name: 'businessIntroduce',
          query: { bizType }
        });
        return;
      }
      if (bizType) {
        import('@/common/flowMixinV2.js').then((a) => {
          a.initFlow.call(this, { bizType, initJumpMode: '0' });
        });
        return;
      }
      if (type === '2') {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '11',
          // targetModule: 'open',
          params: {
            type: 20,
            tradeType: 1
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        return;
      } else if (type === '4') {
        let reqParams = {
          funcNo: '60099',
          actionType: '13',
          params: {
            yjbFuncId: '4002',
            yjbFuncParams: {
              type: 20,
              tradeType: 2 // 1跳转银证转账 2跳转信用银证转账
            }
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        return;
      }
      let targetUrl = '';
      if (url.includes('http')) {
        targetUrl = url;
      } else {
        targetUrl = $hvue.customConfig.targetUrl + url;
      }
      if ($hvue.platform == 0) {
        if ($h.getSession('channelType') === '2095000000000') {
          targetUrl = targetUrl + '&sd_token=' + $h.getSession('authorization');
          // 支付宝渠道
          AlipayJSBridge.call('pushWindow', {
            url: targetUrl,
            param: {
              readTitle: true
            }
          });
        } else {
          $h.setSession('store', JSON.stringify(this.$store.state));
          window.location.href = targetUrl;
        }
      } else {
        // let params = {
        //   actionType: '6',
        //   url: targetUrl
        //   // leftType: 1,
        //   // rightType: 99,
        //   // rightText: ''
        // };
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          // targetModule: 'open',
          params: {
            url: targetUrl,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },

    async goToUrl(url, type, userInfo) {
      let { targetUrlSzbxje, jttStatus } = userInfo;
      // 判断是否是设置保留金额
      if (targetUrlSzbxje && targetUrlSzbxje === url) {
        if (!url.includes('http')) {
          url = $hvue.customConfig.targetUrl + url;
        }
        // let app_id = $h.getSession('opStation') || '';
        let op_station = sessionStorage.getItem('originOpStation');
        let channel_type = $h.getSession('channelType') || '';
        const { appId, instantToken } = await getInstantToken();
        url = addUrlParam(url, 'app_id', appId);
        url = addUrlParam(url, 'op_station', op_station);
        url = addUrlParam(url, 'channel_type', channel_type);
        url = addUrlParam(url, 'instant_token', instantToken);
        console.log('url', JSON.stringify(url));
        // 是否开通金腾通账号
        if (jttStatus && jttStatus === '0') {
          this.$TAlert({
            tips: '您当前暂无现金理财账户，若要使用设置保留金额功能，需先开通现金理财账户，请确认是否继续开通。',
            confirmBtn: '确定',
            hasCancel: true,
            cancelBtn: '取消',
            confirm: () => {
              jumpThirdPartyUrl({
                url
              });
            },
            cancel: () => {}
          });
        } else {
          jumpThirdPartyUrl({
            url
          });
        }
        return;
      }
      this.toUrl(url, type, userInfo);
    },

    renderingView() {
      let _this = this;
      const { inProperty } = this.tkFlowInfo();
      const { bizType } = inProperty;
      this.bizType = bizType;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      businessEgliCheck({
        flowToken
      })
        .then((data) => {
          _hvueLoading.close();
          _this.pageShow = true;
          _this.viewList = data.data.result;

          let passFlag = false;
          const filterList = _this.viewList.filter(
            ({ ruleResult, ruleResultDesc }) =>
              ruleResult !== '1' && JSON.parse(ruleResultDesc).title
          );
          if (filterList.length !== 0) {
            passFlag = filterList.every(
              ({ userInfo }) =>
                userInfo.confirmLabel !== '确认无误' && userInfo.warning === '1'
            );
          }
          if (passFlag) {
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: true });
            return;
          }

          const strategyResult = data.data.strategyResult;
          if (strategyResult === RULE_RESULT.pass) {
            _this.allPass = true;
            if (this.$attrs.isAutoNext) {
              this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            }
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: true });
          } else {
            _this.viewList = data.data.result;
            if (
              $hvue.platform === '0' &&
              $h.getSession('appId') !== 'yjbwx' &&
              $h
                .getSession('history_list')
                .records[$h.getSession('history_list').index].path.includes(
                  '010013'
                )
            ) {
              this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
              return;
            }
            this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
              text: '返回首页',
              display: true,
              btnStatus: 2,
              data: () => {
                this.eventMessage(this, EVENT_NAME.TO_INDEX);
              }
            });
          }
        })
        .catch((err) => {
          _hvueLoading.close();
          _hvueToast({ mes: err });
        });
      this.refreshShow();
    },

    isShowJumpBtn({ userInfo }) {
      if (
        userInfo.targetType &&
        userInfo.targetType === '2' &&
        $hvue.platform === '0'
      ) {
        return false;
      }
      return userInfo.targetType && userInfo.targetLabel;
    },

    checkNormPage(propKey) {
      return ['tradingDate', 'averageAsset'].includes(propKey);
    },
    callOutBtn() {
      preCommitAccountCallout({})
        .then(({ code, msg }) => {
          if (code === 0) {
            this.$TAlert({
              title: '温馨提示',
              tips: '预约成功，请注意接听95310来电。',
              confirm: () => {
                this.eventMessage(this, EVENT_NAME.TO_INDEX);
              }
            });
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((e) => {
          this.$TAlert({
            title: '温馨提示',
            tips: e
          });
        });
    },
    isShowJumpBtnForsfcg() {
      if ($hvue.platform === '0') {
        return false;
      } else {
        return true;
      }
    }
  }
};
</script>

<style scoped>
.sfcg_btns {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sfcg_btn {
  margin-top: 0.1rem;
  font-size: 0.14rem;
  line-height: 0.28rem;
  background: none;
  padding: 0 0.12rem;
  color: var(--buttonBg1, #1061ff);
  background: var(--buttonAssistBg, #ffeded);
  border-radius: 0.5rem;
  z-index: 50;
}
</style>
