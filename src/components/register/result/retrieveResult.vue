<template>
  <div class="info_compage">
    <div class="com_title">
      <h5>亲，您的资金账号如下，请妥善保存</h5>
    </div>
    <div class="input_form">
      <div class="input_text text">
        <span class="tit active">姓名</span>
        <input v-model="$attrs.userName" class="t1" type="tel" readonly />
      </div>
      <div class="input_text text">
        <span class="tit active">资金账号</span>
        <div class="t1 account_break_word">{{ fundAccount }}</div>
        <span class="copy_btn" :data-clipboard-text="fundAccount" @click="copyValue">复制</span>
      </div>
    </div>
  </div>
</template>

<script>
import {
  retrieveFundAccountSendMobile,
  flowQueryIns,
  retrieveFundAccountList,
  addClientCritMark
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import ClipboardJS from 'clipboard';
import { MARK_TYPE } from '@/common/enumeration';

export default {
  name: 'retrieveResult',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      flowInsId: '',
      fundAccount: ''
    };
  },
  created() {
    if ($hvue.platform === '0') this.copyFundAccount();
  },
  mounted() {
    const flowToken = sessionStorage.getItem('TKFlowToken');
    addClientCritMark({
      flowToken,
      markType: MARK_TYPE.FUND_ACCOUNT_RETRIEVE_CONFIRM,
      markContent: '请确认并同意国金证券根据监管要求，收集您的身份信息用于找回资金账号，我们将依法保护您的隐私安全。',
      confirmFlag: '1',
      confirmBtn: '确认'
    });
    flowQueryIns({ flowToken }).then((res) => {
      this.flowInsId = res.data.id;
      // 跑批同步提交
      this.eventMessage(this, EVENT_NAME.NEXT_STEP);
      // next接口出现异常后回调处理
      window.tkFlowNextCallback = (data) => {
        if (data.code === 0) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            text: '发送至手机',
            btnStatus: 2,
            data: this.sendSMS
          });
          this.queryFundAccount();
        } else {
          this.$TAlert({
            tips: data.msg
          });
        }
      };
    });
  },
  methods: {
    queryFundAccount() {
      retrieveFundAccountList({
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then(({ code, msg, data }) => {
          if (code === 0) {
            this.fundAccount = data.retrieveFundAccountList
              .map(({ fundAccount }) => fundAccount)
              .join(',');
          } else {
            Promise.reject(new Error(msg));
          }
        })
        .catch((e) => {
          this.$TAlert({
            title: '温馨提示',
            tips: e
          });
        });
    },
    sendSMS() {
      const { inProperty } = this.tkFlowInfo();
      const { mobileTel } = inProperty;
      retrieveFundAccountSendMobile({
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then(({ code, msg }) => {
          if (code === 0) {
            this.$TAlert({
              title: '温馨提示',
              tips: `亲，已将您的资金账号发送至您的手机${mobileTel}，请注意查收！`,
              confirm: () => {
                this.eventMessage(this, EVENT_NAME.TO_INDEX);
              }
            });
          } else {
            this.$TAlert({
              title: '温馨提示',
              tips: msg
            });
          }
        })
        .catch((err) => {
          this.$TAlert({
            title: '温馨提示',
            tips: err
          });
        });
    },
    copyFundAccount() {
      let clipboard = new ClipboardJS('.copy_btn');
      clipboard.on('success', function (e) {
        console.info('Action:', e.action);
        console.info('Text:', e.text);
        console.info('Trigger:', e.trigger);
        _hvueToast({
          mes: '复制成功'
        });
        e.clearSelection();
      });
      clipboard.on('error', function (e) {
        console.error('Action:', e.action);
        console.error('Trigger:', e.trigger);
      });
    },
    copyValue() {
      if ($hvue.platform !== '0') {
        let reqParams = {
          funcNo: '50130',
          copyText: this.fundAccount
        };
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
        if (res.error_no === '0') {
          _hvueToast({
            mes: '复制成功'
          });
        }
      }
    }
  }
};
</script>

<style scoped>
.copy_btn {
  position: absolute;
  color: #4f7cff;
  right: 0.2rem;
  top: 0.2rem;
}

.account_break_word {
  width: 80%;
  height: auto;
  overflow-wrap: break-word;
  word-break: break-all;
}
</style>
