<template>
  <fragment v-if="!loading">
    <div v-if="pageType === '1'" class="gray_bg">
      <ul class="com_infolist spel">
        <li>
          <span class="tit">股票期权账户</span>
          <p>{{ optAccData.optionFundAccount }}</p>
        </li>
        <li>
          <span class="tit">一码通账户</span>
          <p>{{ optAccData.acodeAccount || '--' }}</p>
        </li>
      </ul>
      <ul class="market_ctlist">
        <li>
          <div class="base">
            <div class="bg">
              <img src="@/assets/images/bg_shanghai.png" />
            </div>
            <h5>上海市场衍生品合约账户</h5>
            <p v-if="shAccInfo.stockAccount">
              {{ shAccInfo.stockAccount }}
            </p>
            <p v-else>未开通</p>
            <span v-if="shAccInfo.holderStatusDesc" class="state">{{
              shAccInfo.holderStatusDesc
            }}</span>
          </div>
        </li>
        <li>
          <div class="base">
            <div class="bg">
              <img src="@/assets/images/bg_shenzhen.png" />
            </div>
            <h5>深圳市场衍生品合约账户</h5>
            <p v-if="szAccInfo.stockAccount">
              {{ szAccInfo.stockAccount }}
            </p>
            <p v-else>未开通</p>
            <span v-if="szAccInfo.holderStatusDesc" class="state">{{
              szAccInfo.holderStatusDesc
            }}</span>
          </div>
          <div class="opea">
            <a v-show="openSZProduct" class="com_btn" @click="toNext"
              >开通账户</a
            >
          </div>
        </li>
      </ul>
      <div
        v-show="shAccInfo.holderStatus && shAccInfo.holderStatus !== '0'"
        class="wx_tips"
      >
        <p>温馨提示：</p>
        <p>
          您当前上海市场衍生品合约账户状态异常，无法办理此业务。如有疑问您可拨打客服95310进行咨询。
        </p>
      </div>
    </div>
    <template v-if="pageType === '2'">
      <section
        class="main fixed qq_bg"
        data-page="home"
        style="position: fixed; z-index: 999"
      >
        <header class="header fixed_header" ref="fixed_header">
          <div class="header_inner">
            <a class="icon_back" @click="back"></a>
            <h1 class="title black_col">股票期权账户</h1>
          </div>
        </header>
        <article class="content" ref="content">
          <div class="qq_hm_page">
            <div class="qq_bannerbox spel">
              <img src="@/assets/images/qq_banner03.png" />
              <div class="txt">
                线上开通深市股票期权账户前，您需临柜开立沪市股票期权账户，您可在按照如下步骤进行预约。
              </div>
            </div>
            <div class="cm_qq_wrap">
              <div class="cm_qq_module">
                <div class="step">STEP 01</div>
                <div class="wrap">
                  <div class="title">
                    <h3><span class="imp">开通</span>期权全真模拟交易账户</h3>
                    <p v-if="!optAccData.stockOptionRealSimulationAccount">
                      无需临柜
                    </p>
                    <p v-else>
                      您已开通：{{
                        optAccData.stockOptionRealSimulationAccount
                      }}
                    </p>
                  </div>
                  <div
                    class="ce_btn"
                    v-if="!optAccData.stockOptionRealSimulationAccount"
                  >
                    <a class="p_button" @click="toBizType('010135')"
                      >立即开通</a
                    >
                  </div>
                  <ul class="n_list">
                    <li>
                      <div class="tit">模拟交易经历：</div>
                      <p v-if="optAccData.optionSimulationTradingExperience">
                        {{ optAccData.optionSimulationTradingExperience }}
                      </p>
                      <p v-else>无交易经历</p>
                      <a
                        class="link"
                        @click="
                          openPage({ url: optAccData.simulationGuideLink })
                        "
                        >模拟指导</a
                      >
                    </li>
                    <li>
                      <div class="tit">期权测试：</div>
                      <p v-if="optAccData.optionKnowledgeTestScore">
                        {{ optAccData.optionKnowledgeTestScore }}
                      </p>
                      <p v-else>无测试成绩</p>
                      <a
                        class="link"
                        @click="
                          openPage({
                            url: optAccData.knowledgeBookLink,
                            title: '国金期权课堂'
                          })
                        "
                        >知识宝典</a
                      >
                    </li>
                  </ul>
                  <div class="n_tips">
                    温馨提示：期权模拟交易经历与测试成绩仅供参考，实际以交易所系统记录为准。
                  </div>
                </div>
              </div>
              <div class="cm_qq_module">
                <div class="step">STEP 02</div>
                <div class="wrap">
                  <div class="title">
                    <h3><span class="imp">开通</span>沪市股票期权账户</h3>
                    <p>需临柜</p>
                  </div>
                  <div class="ce_btn">
                    <a class="p_button" @click="toBizType('010169')"
                      >立即预约</a
                    >
                  </div>
                </div>
              </div>
              <div class="cm_qq_tips">
                <p>申请开立股票期权账户，您需要满足以下条件：</p>
                <p>
                  1、投资者风险承受能力为C3及以上且有效期一年以上（一级交易权限：C3及以上或者专业投资者；二、三级交易权限均需为C4及以上或者专业投资者）；
                </p>
                <p>
                  2、申请开户前20个交易日日均托管在公司的证券市值与资金账户可用余额（不含通过融资融券交易融入的资金或证券），合计不低于人民币50万元；
                </p>
                <p>
                  3、在证券公司开户6个月以上并具备融资融券参与资格或者金融期货交易经历；
                </p>
                <p>4、具备期权基础知识，通过交易所认可的相应等级知识测试；</p>
                <p>5、具有交易所认可的期权模拟交易经历；</p>
                <p>
                  6、不存在严重不良诚信记录，不存在法律、法规、规章和交易所业务规则禁止或者限制从事期权交易的情形。
                </p>
              </div>
              <div class="qq_adv_img">
                <a
                  @click="
                    openPage({
                      url: optAccData.optionEduColumnLink,
                      title: '国金期权课堂'
                    })
                  "
                  ><img src="@/assets/images/cm_qq_img.png"
                /></a>
              </div>
            </div>
          </div>
        </article>
      </section>
    </template>
    <div v-else-if="pageType === '4'">
      <div style="height: 100vh; background: #ffffff; text-align: center">
        <div class="acct_nodata">
          <div class="icon"><img src="@/assets/images/noData2.svg" /></div>
          <h5>
            检测到您在中登沪市股票期权账户已开满3户，请前往原开户券商销户后再来办理。
          </h5>
        </div>
      </div>
    </div>
  </fragment>
</template>

<script>
import { optionAccountQueryV2 } from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import { EXCHANGE_TYPE } from '@/common/enumeration';
import { openNewPage } from '@/common/util';

export default {
  name: 'OptAccOpenV2',
  inject: ['eventMessage'],
  data() {
    return {
      pageType: '', //1、开通期权账户；2、展示期权开户指引页面；3、直接进入预约沪市期权功能；4、中登满3户拦截提醒
      loading: true,
      optAccData: {}
    };
  },
  computed: {
    shAccInfo() {
      return (
        this.optAccData.optionAccountInfoList.filter(
          ({ exchangeType }) => EXCHANGE_TYPE.SH === exchangeType
        )[0] || {}
      );
    },
    szAccInfo() {
      return (
        this.optAccData.optionAccountInfoList.filter(
          ({ exchangeType }) => EXCHANGE_TYPE.SZ === exchangeType
        )[0] || {}
      );
    },
    toNextFlag() {
      return (
        this.shAccInfo?.holderStatus === '0' && !this.szAccInfo.stockAccount
      );
    },
    openSZProduct() {
      return (
        this.toNextFlag ||
        (!this.shAccInfo.stockAccount && !this.szAccInfo.stockAccount)
      );
    }
  },
  watch: {},
  created() {
    this.eventMessage(this, EVENT_NAME.NEXT_BTN, { display: false });
    this.eventMessage(this, EVENT_NAME.BACK_BTN, { event: this.back });
  },
  mounted() {
    this.renderingView();
  },
  beforeDestroy() {
    this.removeScrollListener();
  },
  methods: {
    back() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    renderingView() {
      optionAccountQueryV2({}, { filter: true })
        .then(({ data, code, msg }) => {
          this.loading = false;
          if (code === 0) {
            this.pageType = data.type;
            this.optAccData = data;
            if (this.pageType === '2') {
              // type为2，展示期权开户指引页面
              this.$nextTick(() => {
                this.initScrollListener();
              });
            } else if (this.pageType === '3') {
              // type为3，直接进入预约沪市期权功能
              this.toBizType('010169', '1');
            } else if (this.pageType === '4') {
              this.$store.commit('flow/setWhiteBg', true);
              this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
                display: true,
                text: '返回首页',
                btnStatus: 2,
                data: this.back
              });
            }
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    jumpPage() {
      if ($hvue.platform === '0') {
        window.location.href =
          $hvue.customConfig.thirdPartyUrl.businessDepartment;
      } else {
        let reqParams = {
          funcNo: '60099',
          moduleName: $hvue.customConfig.moduleName,
          actionType: '6',
          params: {
            url: $hvue.customConfig.thirdPartyUrl.businessDepartment,
            leftType: 1,
            rightType: 99,
            rightText: ''
          }
        };
        console.log(`请求参数为: ~~${JSON.stringify(reqParams)}`);
        const res = $h.callMessageNative(reqParams);
        console.log(`请求结果为: ~~${JSON.stringify(res)}`);
      }
    },
    toNext() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        csdc_acode_account: this.optAccData.acodeAccount,
        fund_account_data: JSON.stringify([
          {
            assetProp: this.optAccData.assetProp,
            fundAccount: this.optAccData.optionFundAccount
          }
        ])
      });
    },
    toBizType(bizType, initJumpMode = '0') {
      import('@/common/flowMixinV2.js').then((a) => {
        a.initFlow.call(this, { bizType, initJumpMode });
      });
    },
    openPage({ url = '', title = '' }) {
      if (url === '') return;
      openNewPage({ pageUrl: url, title });
    },
    initScrollListener() {
      if (this.pageType === '2' && this.$refs.content) {
        this.handleScroll = () => {
          const scrollTop = this.$refs.content.scrollTop;
          const fixedHeader = this.$refs.fixed_header;
          if (fixedHeader) {
            if (scrollTop >= 50) {
              fixedHeader.classList.add('active');
            } else {
              fixedHeader.classList.remove('active');
            }
          }
        };
        this.$refs.content.addEventListener('scroll', this.handleScroll);
      }
    },
    removeScrollListener() {
      if (this.$refs.content && this.handleScroll) {
        this.$refs.content.removeEventListener('scroll', this.handleScroll);
      }
    }
  }
};
</script>

<style scoped>
.gray_bg {
  background-color: #f4f4f4;
}
</style>
