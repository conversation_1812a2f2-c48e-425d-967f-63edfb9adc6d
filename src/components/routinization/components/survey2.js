const survey = {
  surveyId: '2', //问卷编号
  surveyTitle: '机构户程序化交易问卷', //问卷标题
  clientId: '', //客户号
  fundAccount: '', //资金账号
  version: '9',
  questionArr: [
    {
      questionId: '200',
      questionTitle: '本次填报类型为',
      type: 'radio',
      inputType: '',
      maxLength: 500,
      options: ['首次填报/变更填报','停用填报'],
      answerArr: [],
      other: '',
      data: '2001'
    },
    {
      questionId: '201',
      questionTitle: '自有资金规模（万元）：请在输入框中输入数字。',
      type: 'input',
      inputType: 'number',
      maxLength: 15,
      minType: '<=',
      options: [],
      answerArr: [],
      other: '',
      data: null,
      unitIcon: 'ten_thousand_yuan'
    },
    {
      questionId: '202',
      questionTitle:
        '募集资金规模（万元）：请在输入框中输入数字。<b style="color:red;font-weight: bold;">如您没有募集资金，请在输入框中填写0；</b>',
      type: 'input',
      inputType: 'number',
      maxLength: 15,
      minType: '<',
      options: [],
      answerArr: [],
      disabled: true,
      other: '',
      data: '0',
      unitIcon: 'ten_thousand_yuan'
    },
    {
      questionId: '203',
      questionTitle:
        '杠杆资金规模（万元）：请在输入框中输入数字。<b style="color:red;font-weight: bold;">如您没有杠杆资金，请在输入框中填写0；</b>',
      type: 'input',
      inputType: 'number',
      maxLength: 15,
      minType: '<',
      options: [],
      answerArr: [],
      other: '',
      data: null,
      unitIcon: 'ten_thousand_yuan'
    },
    {
      questionId: '204',
      questionTitle:
        '其他资金规模（万元）：请在输入框中输入数字。<b style="color:red;font-weight: bold;">如您没有其他资金来源 ，请在输入框中填写0；</b>',
      type: 'input',
      inputType: 'number',
      maxLength: 15,
      minType: '<',
      options: [],
      answerArr: [],
      other: '',
      data: null,
      unitIcon: 'ten_thousand_yuan'
    },
    {
      questionId: '205',
      questionTitle: '其他资金来源：请在输入框中输入其他资金来源，不超过30字',
      type: 'input',
      inputType: 'textarea',
      maxLength: 30,
      options: [],
      answerArr: [],
      canDisabledId: '其他资金规模', // 是否可操作与04题相关
      other: '',
      data: null
    },
    {
      questionId: '206',
      questionTitle:
        '杠杆资金来源：当选择“其他”时，请在输入框中输入杠杆资金来源，不超过30字。',
      type: 'checkbox',
      inputType: 'textarea',
      maxLength: 30,
      options: ['融资融券', '场外衍生品', '其他'],
      answerArr: [],
      canDisabledId: '杠杆资金规模', // 是否可操作与04题相关
      other: '',
      data: []
    },
    {
      questionId: '207',
      questionTitle: '股票市场交易品种',
      type: 'checkbox',
      inputType: '',
      maxLength: 500,
      options: ['股票', '基金', '存托凭证'],
      answerArr: [],
      other: '',
      data: []
    },
    {
      questionId: '208',
      questionTitle: '是否量化交易',
      type: 'radio',
      inputType: '',
      maxLength: 500,
      options: ['是', '否'],
      answerArr: [],
      other: '',
      data: null
    },
    {
      questionId: '209',
      questionTitle:
        '主策略类型：当选择“其他”时，请在输入框中输入主策略名称，不超过20字。',
      type: 'radio',
      inputType: 'textarea',
      maxLength: 20,
      options: [
        '非量化交易',
        '指数增强策略',
        '市场中性策略',
        '多空灵活策略',
        '量化多头策略',
        '管理期货策略CTA',
        '参与新股发行策略',
        '量化套利策略',
        '日内回转策略',
        '其他'
      ],
      answerArr: [],
      canDisabledId: '是否量化交易', // 是否可操作与08题相关
      other: '',
      data: null
    },
    {
      questionId: '210',
      questionTitle:
        '主策略概述：请简要解释所采用策略的主要内容，不超过200字。',
      type: 'input',
      inputType: 'textarea',
      maxLength: 200,
      options: [],
      answerArr: [],
      canDisabledId: '是否量化交易',
      other: '',
      data: null
    },
    {
      questionId: '211',
      questionTitle:
        '辅策略类型：最多选择2项，当选择“其他”时，请在输入框中输入辅策略名称，不超过50字。',
      type: 'checkbox',
      inputType: 'textarea',
      maxLength: 50,
      options: [
        '非量化交易',
        '指数增强策略',
        '市场中性策略',
        '多空灵活策略',
        '量化多头策略',
        '管理期货策略CTA',
        '参与新股发行策略',
        '量化套利策略',
        '日内回转策略',
        '其他'
      ],
      max: 2, //最多可选两个
      answerArr: [],
      canDisabledId: '是否量化交易',
      // 条件性必填配置：当依赖题目选择指定选项时，此题目变为非必填
      conditionalRequired: {
        dependsOnQuestionId: '208', // 依赖的题目ID（是否量化交易）
        whenOptionSelected: '1', // 当选择选项1（是）时
        isRequired: false // 此题目变为非必填
      },
      other: '',
      data: []
    },
    {
      questionId: '212',
      questionTitle:
        '辅策略概述：请简要解释所采用策略的主要内容，不超过200字。',
      type: 'input',
      inputType: 'textarea',
      maxLength: 200,
      options: [],
      answerArr: [],
      canDisabledId: '是否量化交易',
      // 条件性必填配置：当依赖题目选择指定选项时，此题目变为非必填
      conditionalRequired: {
        dependsOnQuestionId: '208', // 依赖的题目ID（是否量化交易）
        whenOptionSelected: '1', // 当选择选项1（是）时
        isRequired: false // 此题目变为非必填
      },
      other: '',
      data: null
    },
    {
      questionId: '213',
      questionTitle:
        '期货市场账户名称：若“主策略类型”、“辅策略类型”字段填写了“市场中性策略”、“多空灵活策略”、“管理期货策略CTA”，则“期货市场账户名称”则必须填报。如没有期货（含期权）市场账户，请选择无；若有期货（含期权）账户，请选择其他，并填写账户名称，可填写多个账户，不超过200字。个人客户的账户名称请填写本人姓名。',
      type: 'radio',
      inputType: 'textarea',
      maxLength: 200,
      options: ['无', '其他'],
      answerArr: [],
      other: '',
      data: null
    },
    {
      questionId: '214',
      questionTitle:
        '期货市场账户代码：若“主策略类型”、“辅策略类型”字段填写了“市场中性策略”、“多空灵活策略”、“管理期货策略CTA”，则“期货市场账户名称”则必须填报。如没有期货（含期权）市场账户，请选择无；若有期货（含期权）账户，请选择其他，并填写账户代码，可填写多个账户，不超过200字。如果是个人客户，填本人期货账户中金所交易编码，如不知晓建议联系期货公司咨询。',
      type: 'radio',
      inputType: 'textarea',
      maxLength: 200,
      options: ['无', '其他'],
      answerArr: [],
      other: '',
      data: null
    },
    {
      questionId: '226',
      questionTitle: '是否使用算法方式执行交易指令？',
      type: 'radio',
      inputType: '',
      maxLength: 200,
      options: ['是', '否'],
      answerArr: [],
      other: '',
      data: null
    },
    {
      questionId: '215',
      questionTitle:
        '交易指令执行方式：当选择“其他”时，请在输入框中输入交易指令执行方式，不超过50字。',
      type: 'checkbox',
      inputType: 'textarea',
      maxLength: 50,
      options: ['TWAP', 'VWAP', 'POV', '其他'],
      answerArr: [],
      other: '',
      data: [],
      // 条件性必填配置：当依赖题目选择"否"时，此题目变为非必填
      conditionalRequired: {
        dependsOnQuestionId: '226', // 依赖的题目ID（是否使用算法方式执行交易指令）
        whenOptionSelected: '2', // 当选择选项2（否）时
        isRequired: false // 此题目变为非必填
      }
    },
    {
      questionId: '216',
      questionTitle:
        '指令执行方式概述：请简要解释所采用指令执行方式的主要内容，不超过200字。',
      type: 'input',
      inputType: 'textarea',
      maxLength: 200,
      options: [],
      answerArr: [],
      other: '',
      data: null,
      // 条件性必填配置：当依赖题目选择"否"时，此题目变为非必填
      conditionalRequired: {
        dependsOnQuestionId: '226', // 依赖的题目ID（是否使用算法方式执行交易指令）
        whenOptionSelected: '2', // 当选择选项2（否）时
        isRequired: false // 此题目变为非必填
      }
    },
    {
      questionId: '217',
      questionTitle: '账户最高申报速率（笔/秒）',
      type: 'radio',
      inputType: '',
      maxLength: 500,
      options: ['500笔及以上', '300笔至499笔', '100笔至299笔', '100笔以下'],
      answerArr: [],
      other: '',
      data: null
    },
    {
      questionId: '218',
      questionTitle: '账户单日最高申报笔数',
      type: 'radio',
      inputType: '',
      maxLength: 500,
      options: [
        '25000笔及以上',
        '20000笔至24999笔',
        '15000笔至19999笔',
        '10000笔至14999笔',
        '10000笔以下'
      ],
      answerArr: [],
      other: '',
      data: null
    },
    {
      questionId: '219',
      questionTitle:
        '程序化交易软件名称及版本号',
      type: 'checkbox',
      max: 5, //最多可选5个
      inputType: 'textarea',
      maxLength: 160,
      options: [
        '国金QMT-V1.0.0',
        '国金万得宏汇交易系统-GJZQWtt4.10_2023',
        '国金恒生PB-PF1.0V202001.03',
        '国金迅投PB-V4.0.0',
        '国金络町-络町V2.1',
        '国金证券智能算法平台-i2-ALGO1.0V202401.03.004',
        // '其他'
        '国金PTrade-PTrade1.0-ClientV202302',
        '国金卡方ATX-国金证券ATXV2.0.4',
        '国金佣金宝-V8',
        '国金全能行远航版-V9',
        '同花顺-V9',
        '国金太阳网上交易系统至强版经典款-V10',
        '全能行APP端-V9',
        '同花顺APP-V11',
      ],
      answerArr: [],
      other: '',
      data: []
    },
    {
      questionId: '220',
      questionTitle:
        '程序化交易软件开发主体',
      type: 'checkbox',
      disabled: true,
      inputType: 'textarea',
      maxLength: 500,
      options: [
        '国金QMT：北京睿智融科控股有限公司',
        '国金万得宏汇交易系统：上海万得宏汇信息技术有限公司',
        '国金恒生PB：恒生电子股份有限公司',
        '国金迅投PB：北京睿智融科控股有限公司',
        '国金络町：杭州络町软件科技有限责任公司',
        '国金证券智能算法平台：恒生电子股份有限公司',
        // '其他'
        '国金PTrade：杭州云纪网络科技有限公司',
        '国金卡方ATX：上海卡方信息科技有限公司',
        '国金佣金宝：国金证券股份有限公司',
        '国金全能行远航版：浙江核新同花顺网络信息股份有限公司',
        '同花顺：浙江核新同花顺网络信息股份有限公司',
        '国金太阳网上交易系统至强版经典款：深圳市财富趋势科技股份有限公司',
        '全能行APP端：浙江核新同花顺网络信息股份有限公司',
        '同花顺APP：浙江核新同花顺网络信息股份有限公司',
      ],
      answerArr: [],
      other: '',
      data: []
    },
    {
      questionId: '221',
      questionTitle:
        '程序化交易系统服务器所在地',
      // type: 'radio',
      type: 'checkbox',
      disabled: true,
      inputType: 'textarea',
      maxLength: 100,
      options: ['上海市浦东新区荷丹路130号世纪互联外高桥保税区荷丹数据中心', '上海市浦东新区龙沪路399号金桥数据中心'],
      answerArr: [],
      other: '',
      data: []
    },
    {
      questionId: '222',
      questionTitle:
        '投资者相关业务负责人：请填写机构程序化交易业务负责人名字。',
      type: 'input',
      inputType: 'textarea',
      maxLength: 80,
      options: [],
      answerArr: [],
      other: '',
      data: null
    },
    {
      questionId: '223',
      questionTitle: '联系电话：请填写机构程序化交易业务负责人的联系电话。',
      type: 'input',
      inputType: 'textarea',
      maxLength: 80,
      options: [],
      answerArr: [],
      other: '',
      data: null
    },
    {
      questionId: '225',
      questionTitle: '其他交易品种',
      type: 'radio',
      inputType: '',
      maxLength: 500,
      options: ['含可转债', '不含可转债'],
      answerArr: [],
      other: '',
      data: []
    },
    // 可转债224
    {
      questionId: '224',
      questionTitle: '可转债交易策略：当选择“其他”时，请在输入框中输入策略名称，不超过20字。',
      type: 'checkbox',
      inputType: 'textarea',
      maxLength: 20,
      options: ['基本面分析策略（Alpha策略）', '日内回转策略', '套利类策略','趋势类策略','做市类策略','其他'],
      answerArr: [],
      other: '',
      data: [],
      hidden: true,
    },
  ]
};
export default survey;
