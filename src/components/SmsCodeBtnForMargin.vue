<template>
  <a class="code_btn2" :class="{ disabled: countdown }" :style="cssCode" @click="sendSMS">{{ countdown ?
    `重新发送${countdown}` : '获取验证码' }}
  </a>
</template>

<script>
import { codeSending, safeSmsSend } from '@/service/service';
import { SMS_SEND_TYPE } from '@/common/enumeration';

export default {
  name: 'SmsCodeBtnForMargin',
  model: {
    prop: 'uuid',
    event: 'change'
  },
  props: {
    needImgCode: {
      type: Boolean,
      default: false
    },
    uuid: {
      type: String,
      default: ''
    },
    cssCode: {
      type: Object,
      default: () => { }
    },
    mobileNo: {
      type: String | Number,
      default: ''
    },
    bizType: {
      type: String,
      default: ''
    },
    captcha: {
      type: String,
      default: ''
    },
    captchaToken: {
      type: String,
      default: ''
    },
    sendBefore: {
      type: Function,
      default: () => {
        return true;
      }
    }
  },
  data() {
    return {
      count: 0, // 短信验证码倒计时
    };
  },
  computed: {
    countdown() {
      if (this.count <= 0) {
        return false;
      } else {
        return `（${this.count}）`;
      }
    }
  },
  methods: {
    sendSMS() {
      if (!/1[3-9][\d]{9}/.test(this.mobileNo)) {
        _hvueToast({
          mes: '手机号格式不正确'
        });
        return false;
      }
      if (!this.sendBefore()) {
        this.$emit('send-result', false);
        return false;
      }
      if (this.countdown) return;
      if (this.needImgCode) {
        if (!this.captcha) {
          _hvueToast({
            mes: '请输入图形验证码'
          });
          // this.$emit('send-result', false);
          return false;
        }
        if (this.captcha.length < 4) {
          _hvueToast({
            mes: '图形验证码格式不正确'
          });
          // this.$emit('send-result', false);
          return false;
        }
        safeSmsSend({
          mobileNo: this.mobileNo,
          bizType: this.bizType,
          smsSendType: SMS_SEND_TYPE.SMS,
          captcha: this.captcha,
          captchaToken: this.captchaToken
        })
          .then((data) => {
            const uuid = data.data.serialNumber;
            this.count = $hvue.customConfig.sendSMSCount || 0;
            const timer = setInterval(() => {
              if (this.count <= 0) {
                clearInterval(timer);
              } else {
                this.count--;
              }
            }, 1000);
            this.$once('hook:deactivated', () => {
              clearInterval(timer);
            });
            this.$emit('change', uuid);
            this.$emit('send-result', true);
          })
          .catch((error) => {
            _hvueToast({
              mes: error
            });
            this.$emit('send-result', false);
          });
        return;
      }
      codeSending({
        mobile: this.mobileNo,
        expireMinutes: 10,
        captcha: this.captcha,
        captchaToken: this.captchaToken
      })
        .then((data) => {
          const uuid = data.data.serialNumber;
          this.count = $hvue.customConfig.sendSMSCount || 0;
          const timer = setInterval(() => {
            if (this.count <= 0) {
              clearInterval(timer);
            } else {
              this.count--;
            }
          }, 1000);
          this.$once('hook:deactivated', () => {
            clearInterval(timer);
          });
          this.$emit('change', uuid);
          this.$emit('send-result', true);
        })
        .catch((error) => {
          _hvueToast({
            mes: error
          });
          this.$emit('send-result', false);
        });
    }
  }
};
</script>
