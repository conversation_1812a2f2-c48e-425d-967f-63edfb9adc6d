import Vue from 'vue';
import App from './App';
import router from './router';
import store from './store';
import Fragment from 'vue-fragment';
import * as filters from '@/common/filter';
import xss from 'xss';
import TAlert from '@/components/TAlert/alert.js';
import THeader from '@/components/register/THeader';
// 引入拓展库
import '@/extends/index';
// import { HKeypanel } from 'thinkive-hui';

// 项目css文件
import './assets/css/iconfont.css';
import './assets/css/style.less';

Vue.prototype.$TAlert = TAlert;
Vue.component('THeader', THeader);
Vue.use(Fragment.Plugin);
Vue.directive('throttle', {
  // 添加指令用于对点击事件节流
  bind: function (el, binding) {
    let time = binding.value || 1000;
    let preTime = new Date().getTime();
    el.addEventListener('touchstart', (e) => {
      const nowTime = new Date().getTime();
      if (preTime && nowTime - preTime < time) {
        e.preventDefault();
        e.stopImmediatePropagation();
      } else {
        preTime = nowTime;
      }
    });
  }
});

Vue.prototype.xss = xss;
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});

Vue.config.productionTip = false;
// 解决ios下css: active伪类无效果问题
document.addEventListener('touchstart', function () {}, { passive: false });
/* eslint-disable no-new */

function getNetworkRouter() {
  const origin = window.location.origin;
  const host = window.location.host;
  // 环境配置映射表
  const ENV_CONFIG = {
    sit: {
      hosts: ['https://fzsdbusinesscdn.yjbtest.com', 'https://fzsdbusiness.yjbtest.com'],
      apiHost: 'https://apibizsit.yjbtest.com:10443'
    },
    uat: {
      hosts: ['https://uatfzsdbusinesscdn.yjbtest.com', 'https://uatfzsdbusiness.yjbtest.com'],
      apiHost: 'https://uatfzsdbusiness.yjbtest.com'
    },
    uat2: {
      hosts: [
        'https://uat2fzsdbusinesscdn.yjbtest.com',
        'https://uat2fzsdbusiness.yjbtest.com'
      ],
      apiHost: 'https://uat2fzsdbusiness.yjbtest.com'
    },
    uat3: {
      hosts: [
        'https://uat3fzsdbusinesscdn.yjbtest.com',
        'https://uat3fzsdbusiness.yjbtest.com'
      ],
      apiHost: 'https://uat3fzsdbusiness.yjbtest.com'
    },
    gray: {
      hosts: [
        'https://preacctbizcdn.yongjinbao.com.cn',
        'https://preacctbiz.yongjinbao.com.cn'
      ],
      apiHost: 'https://preacctbiz.yongjinbao.com.cn'
    },
    prod: {
      hosts: ['https://acctbizcdn.yongjinbao.com.cn', 'https://acctbiz.yongjinbao.com.cn'],
      apiHost: 'https://acctbiz.yongjinbao.com.cn'
    }
  };

  /**
   * 检查当前域名是否匹配指定环境的主机列表
   * @param {string[]} hosts - 主机列表
   * @returns {boolean} 是否匹配
   */
  const isHostMatched = (hosts) => {
    return hosts.some((hostItem) => origin.includes(hostItem));
  };

  // 默认配置
  const defaultOptions = {
    dev: PACK_ENV === 'local' || isHostMatched(ENV_CONFIG.sit.hosts),
    target: 'https://fzwebapps.yjbtest.com',
    server: '',
    financialServer:
      'https://fzmall.yjbtest.com/mall-web/financial-market/quoted',
    financingFacilityServer: 'https://fzwx3g.yjbtest.com', // 两融开户地址
    channelDomain: 'https://fzfinder.yjbtest.com', // 火山埋点上报
    miniProgramType: 2 // 小程序的版本  0-正式；1-开发；2-体验
  };

  // 生产环境配置
  const prodOptions = {
    target: 'https://webapps.yongjinbao.com.cn',
    financialServer:
      'https://mall.yongjinbao.com.cn/mall-web/financial-market/quoted',
    financingFacilityServer: 'https://wx3g.yongjinbao.com.cn',
    channelDomain: 'https://finder.yongjinbao.com.cn',
    miniProgramType: 0
  };

  // 环境检测和配置应用
  let options = { ...defaultOptions };

  if (isHostMatched(ENV_CONFIG.uat.hosts)) {
    options.server = ENV_CONFIG.uat.apiHost;
  } else if (isHostMatched(ENV_CONFIG.uat2.hosts)) {
    options.server = ENV_CONFIG.uat2.apiHost;
  } else if (isHostMatched(ENV_CONFIG.uat3.hosts)) {
    options.server = ENV_CONFIG.uat3.apiHost;
  } else if (
    isHostMatched(ENV_CONFIG.gray.hosts) ||
    isHostMatched(ENV_CONFIG.prod.hosts)
  ) {
    // 生产和灰度环境配置
    Object.assign(options, prodOptions);
    options.server = isHostMatched(ENV_CONFIG.gray.hosts)
      ? ENV_CONFIG.gray.apiHost
      : ENV_CONFIG.prod.apiHost;
  } else if (isHostMatched(ENV_CONFIG.sit.hosts) || options.dev) {
    options.server = ENV_CONFIG.sit.apiHost;
  } else {
    options.server = ENV_CONFIG.prod.apiHost;
  }
  return options;
}
window.serviceOptions = getNetworkRouter();

// 注册Vue实例
loadJS('/bc-h5-view/views/configuration_' + BUILD_HASH + '.js')
  .then(() => {
    loadJS('/bc-h5-view/views/theme.js')
      .then(() => {
        window.vm = new Vue({
          router,
          store,
          render: (h) => h(App)
        });
        window.vm.$mount('#app');
      })
      .catch((e) => {
        if (confirm('theme.js错误' + e.name)) {
          window.location.reload();
        }
      });
  })
  .catch((e) => {
    if (confirm('加载configuration.js错误' + e.name)) {
      window.location.reload();
    }
  });

/**
 * 添加日期格式化方法
 */
// eslint-disable-next-line no-extend-native
Date.prototype.format = function (fmt) {
  var o = {
    'M+': this.getMonth() + 1, // 月份
    'd+': this.getDate(), // 日
    'h+': this.getHours(), // 小时
    'm+': this.getMinutes(), // 分
    's+': this.getSeconds(), // 秒
    'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
    S: this.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (this.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return fmt;
};

function loadJS(urls) {
  if (!Array.isArray(urls)) {
    urls = [urls];
  }
  let success = 0;
  return new Promise(function (resolve, reject) {
    urls.every((url) => {
      createScriptElement(
        url,
        function () {
          ++success;
          if (success === urls.length) {
            resolve();
          }
        },
        function (e) {
          reject(e);
        }
      );
      return true;
    });
  });
}

function createScriptElement(url, success, fail) {
  var script = document.createElement('script');
  script.type = 'text/javascript';
  script.onload = function () {
    success();
  };
  script.onerror = function (e) {
    fail(e);
  };
  script.src = url;
  document.getElementsByTagName('head')[0].appendChild(script);
}
